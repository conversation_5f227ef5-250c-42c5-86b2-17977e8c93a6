<?php $__env->startSection('title', 'API Documentation - TiXara'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-pasta-cream via-white to-pasta-mint/30 py-12">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                🚀 TiXara API Documentation
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Comprehensive API documentation for TiXara event ticketing platform. 
                Build amazing applications with our RESTful API.
            </p>
        </div>

        <!-- API Status -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 mb-8">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">API Status</h2>
                <div id="api-status" class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span class="text-green-600 font-medium">Checking...</span>
                </div>
            </div>
            <div id="api-details" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <!-- Endpoints -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Public Endpoints -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <svg class="w-6 h-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Public Endpoints
                </h3>

                <!-- Health Check -->
                <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-900 dark:text-white">Health Check</h4>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">GET</span>
                    </div>
                    <code class="text-sm text-gray-600 dark:text-gray-300">/api/health-check</code>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">Basic API health status</p>
                </div>

                <!-- Status -->
                <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-900 dark:text-white">Detailed Status</h4>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">GET</span>
                    </div>
                    <code class="text-sm text-gray-600 dark:text-gray-300">/api/status</code>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">Comprehensive system status with database and cache info</p>
                </div>

                <!-- Search -->
                <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-900 dark:text-white">Search Events</h4>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">GET</span>
                    </div>
                    <code class="text-sm text-gray-600 dark:text-gray-300">/api/search?q={query}</code>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">Search published events by title, description, venue, or city</p>
                    <div class="mt-2">
                        <button onclick="testSearch()" class="px-3 py-1 bg-primary text-white text-xs rounded hover:bg-primary/80 transition-colors">
                            Test Search
                        </button>
                    </div>
                </div>
            </div>

            <!-- Authenticated Endpoints -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <svg class="w-6 h-6 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                    </svg>
                    Authenticated Endpoints
                </h3>

                <!-- User Info -->
                <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-900 dark:text-white">User Info</h4>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">GET</span>
                    </div>
                    <code class="text-sm text-gray-600 dark:text-gray-300">/api/user</code>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">Get authenticated user information</p>
                </div>

                <!-- Notifications -->
                <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-900 dark:text-white">Latest Notifications</h4>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">GET</span>
                    </div>
                    <code class="text-sm text-gray-600 dark:text-gray-300">/api/notifications/latest</code>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">Get user's latest notifications</p>
                </div>

                <!-- Mark as Read -->
                <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-900 dark:text-white">Mark as Read</h4>
                        <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">POST</span>
                    </div>
                    <code class="text-sm text-gray-600 dark:text-gray-300">/api/notifications/{id}/read</code>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">Mark specific notification as read</p>
                </div>

                <!-- Mark All as Read -->
                <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-900 dark:text-white">Mark All as Read</h4>
                        <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">POST</span>
                    </div>
                    <code class="text-sm text-gray-600 dark:text-gray-300">/api/notifications/mark-all-read</code>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">Mark all notifications as read</p>
                </div>
            </div>
        </div>

        <!-- Authentication -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 mt-8">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Authentication</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
                Authenticated endpoints require Laravel Sanctum authentication. Include the following headers:
            </p>
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <pre class="text-sm text-gray-800 dark:text-gray-200"><code>Accept: application/json
X-Requested-With: XMLHttpRequest
X-CSRF-TOKEN: {csrf_token}</code></pre>
            </div>
        </div>

        <!-- Rate Limiting -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 mt-8">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Rate Limiting</h3>
            <p class="text-gray-600 dark:text-gray-300">
                API requests are limited to <strong>60 requests per minute</strong> per IP address or authenticated user.
            </p>
        </div>
    </div>
</div>

<script>
// Load API status
async function loadApiStatus() {
    const statusElement = document.getElementById('api-status');
    const detailsElement = document.getElementById('api-details');
    
    try {
        const response = await fetch('/api/status');
        const data = await response.json();
        
        if (data.status === 'ok') {
            statusElement.innerHTML = `
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <span class="text-green-600 font-medium">Online</span>
            `;
            
            detailsElement.innerHTML = `
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">${data.database.events_count}</div>
                    <div class="text-sm text-gray-500">Events</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">${data.database.users_count}</div>
                    <div class="text-sm text-gray-500">Users</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">${data.database.status.toUpperCase()}</div>
                    <div class="text-sm text-gray-500">Database</div>
                </div>
            `;
        } else {
            statusElement.innerHTML = `
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <span class="text-red-600 font-medium">Offline</span>
            `;
        }
    } catch (error) {
        statusElement.innerHTML = `
            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span class="text-yellow-600 font-medium">Error</span>
        `;
    }
}

// Test search functionality
async function testSearch() {
    try {
        const response = await fetch('/api/search?q=konser');
        const data = await response.json();
        
        alert(`Search test result:\nQuery: ${data.query}\nResults: ${data.count}\nMessage: ${data.message}`);
    } catch (error) {
        alert('Search test failed: ' + error.message);
    }
}

// Load status on page load
document.addEventListener('DOMContentLoaded', loadApiStatus);
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/api/documentation.blade.php ENDPATH**/ ?>