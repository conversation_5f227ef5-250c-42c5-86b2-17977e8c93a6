@extends('layouts.main')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-light via-white to-green-light/30">

    <!-- Event Header -->
    <section class="pt-8 pb-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-6" aria-label="Breadcrumb" data-aos="fade-right">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="text-gray-500 hover:text-primary transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                            </svg>
                            Beranda
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <a href="{{ route('tickets.index') }}" class="ml-1 text-gray-500 hover:text-primary transition-colors duration-200 md:ml-2">Tickets</a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="ml-1 text-gray-700 md:ml-2">{{ $event->title }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </section>

    <!-- Event Content -->
    <section class="pb-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-8">

                    <!-- Event Image & Gallery -->
                    <div class="bg-white rounded-2xl overflow-hidden shadow-lg" data-aos="fade-up">
                        <div class="relative">
                            <img src="{{ $event->poster_url }}"
                                 alt="{{ $event->title }}"
                                 class="w-full h-96 object-cover">

                            <!-- Event Status Overlay -->
                            @if($event->availability_status == 'sold_out')
                                <div class="absolute inset-0 bg-black/60 flex items-center justify-center backdrop-blur-sm">
                                    <div class="text-center text-white animate-pulse">
                                        <div class="w-20 h-20 mx-auto mb-4 bg-red-500 rounded-full flex items-center justify-center">
                                            <i data-lucide="x-circle" class="w-10 h-10"></i>
                                        </div>
                                        <h3 class="text-3xl font-bold mb-2">SOLD OUT</h3>
                                        <p class="text-lg">Tiket sudah habis terjual</p>
                                        <p class="text-sm mt-2 opacity-75">Coba cari event serupa di bawah</p>
                                    </div>
                                </div>
                            @elseif($event->availability_status == 'limited')
                                <div class="absolute top-4 left-4 animate-bounce">
                                    <span class="bg-gradient-to-r from-orange-500 to-red-500 text-white px-4 py-2 rounded-full font-semibold shadow-lg">
                                        <i data-lucide="alert-triangle" class="w-4 h-4 inline mr-1"></i>
                                        Tiket Terbatas!
                                    </span>
                                </div>
                            @elseif($event->availability_status == 'sale_not_active')
                                <div class="absolute top-4 left-4">
                                    <span class="bg-gray-500 text-white px-4 py-2 rounded-full font-semibold shadow-lg">
                                        <i data-lucide="clock" class="w-4 h-4 inline mr-1"></i>
                                        Penjualan Belum Dimulai
                                    </span>
                                </div>
                            @elseif($event->availability_status == 'started')
                                <div class="absolute top-4 left-4">
                                    <span class="bg-blue-500 text-white px-4 py-2 rounded-full font-semibold shadow-lg">
                                        <i data-lucide="play" class="w-4 h-4 inline mr-1"></i>
                                        Event Sedang Berlangsung
                                    </span>
                                </div>
                            @elseif($event->is_featured)
                                <div class="absolute top-4 left-4">
                                    <span class="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-full font-semibold shadow-lg">
                                        <i data-lucide="star" class="w-4 h-4 inline mr-1"></i>
                                        Featured Event
                                    </span>
                                </div>
                            @elseif($event->is_free)
                                <div class="absolute top-4 left-4">
                                    <span class="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-full font-semibold shadow-lg">
                                        <i data-lucide="gift" class="w-4 h-4 inline mr-1"></i>
                                        Gratis
                                    </span>
                                </div>
                            @endif

                            <!-- Wishlist & Share -->
                            <div class="absolute top-4 right-4 flex space-x-2">
                                @auth
                                    <button onclick="toggleWishlist({{ $event->id }})"
                                            class="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white hover:scale-110 transition-all duration-200 wishlist-btn group shadow-lg"
                                            data-event-id="{{ $event->id }}"
                                            data-in-wishlist="{{ $isInWishlist ? 'true' : 'false' }}"
                                            title="{{ $isInWishlist ? 'Hapus dari wishlist' : 'Tambah ke wishlist' }}">
                                        <svg class="w-6 h-6 transition-all duration-200 {{ $isInWishlist ? 'text-red-500 fill-current' : 'text-gray-600 group-hover:text-red-500' }}"
                                             fill="{{ $isInWishlist ? 'currentColor' : 'none' }}"
                                             stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                        </svg>
                                    </button>
                                @else
                                    <a href="{{ route('login') }}"
                                       class="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white hover:scale-110 transition-all duration-200 group shadow-lg"
                                       title="Login untuk menambah ke wishlist">
                                        <svg class="w-6 h-6 text-gray-600 group-hover:text-red-500 transition-colors duration-200"
                                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                        </svg>
                                    </a>
                                @endauth

                                <div class="relative">
                                    <button onclick="toggleShareMenu()"
                                            class="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white hover:scale-110 transition-all duration-200 group shadow-lg"
                                            id="shareButton"
                                            title="Bagikan event">
                                        <svg class="w-6 h-6 text-gray-600 group-hover:text-primary transition-colors duration-200"
                                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                        </svg>
                                    </button>

                                    <!-- Share Menu -->
                                    <div id="shareMenu" class="absolute top-14 right-0 bg-white rounded-lg shadow-xl border border-gray-200 py-2 w-48 hidden z-50">
                                        <button onclick="shareToWhatsApp()" class="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.106"/>
                                                </svg>
                                            </div>
                                            <span class="text-gray-700">WhatsApp</span>
                                        </button>
                                        <button onclick="shareToFacebook()" class="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                                </svg>
                                            </div>
                                            <span class="text-gray-700">Facebook</span>
                                        </button>
                                        <button onclick="shareToTwitter()" class="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center">
                                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                                </svg>
                                            </div>
                                            <span class="text-gray-700">Twitter</span>
                                        </button>
                                        <button onclick="copyEventLink()" class="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center">
                                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                                </svg>
                                            </div>
                                            <span class="text-gray-700">Salin Link</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Gallery Thumbnails -->
                        @if(count($event->gallery_urls) > 0)
                            <div class="p-4 border-t border-gray-200">
                                <div class="flex space-x-2 overflow-x-auto">
                                    @foreach($event->gallery_urls as $image)
                                        <img src="{{ $image }}"
                                             alt="Gallery"
                                             class="w-20 h-20 object-cover rounded-lg cursor-pointer hover:opacity-75 transition-opacity duration-200"
                                             onclick="openGallery('{{ $image }}')">
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Event Details -->
                    <div class="bg-white rounded-2xl shadow-lg p-8" data-aos="fade-up" data-aos-delay="100">
                        <!-- Title & Category -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-4">
                                <span class="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-semibold">
                                    {{ $event->category->name }}
                                </span>
                                @if($event->is_featured)
                                    <span class="bg-purple-100 text-purple-600 px-3 py-1 rounded-full text-sm font-semibold">
                                        Featured Event
                                    </span>
                                @endif
                            </div>
                            <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{{ $event->title }}</h1>

                            <!-- Event Meta -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-gray-600">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                    <div>
                                        <div class="font-semibold">{{ $event->start_date->format('d M Y') }}</div>
                                        <div class="text-sm">{{ $event->start_date->format('H:i') }} - {{ $event->end_date->format('H:i') }}</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                    <div>
                                        <div class="font-semibold">{{ $event->venue_name }}</div>
                                        <div class="text-sm">{{ $event->city }}, {{ $event->province }}</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                    <div>
                                        <div class="font-semibold">{{ $event->organizer->name }}</div>
                                        <div class="text-sm">Organizer</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Deskripsi Event</h3>
                            <div class="prose prose-lg max-w-none text-gray-700">
                                {!! nl2br(e($event->description)) !!}
                            </div>
                        </div>

                        <!-- Tags -->
                        @if($event->tags && count($event->tags) > 0)
                            <div class="mb-8">
                                <h3 class="text-xl font-bold text-gray-900 mb-4">Tags</h3>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($event->tags as $tag)
                                        <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                                            #{{ $tag }}
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Venue Details -->
                        <div>
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Lokasi Event</h3>
                            <div class="bg-gray-50 rounded-xl p-6">
                                <div class="flex items-start space-x-4">
                                    <div class="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-900 mb-2">{{ $event->venue_name }}</h4>
                                        <p class="text-gray-600 mb-4">{{ $event->venue_address }}</p>
                                        <button onclick="openMaps()" class="text-primary hover:text-accent font-semibold transition-colors duration-200">
                                            Lihat di Maps →
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Booking Sidebar -->
                <div class="lg:col-span-1">
                    <div class="sticky top-24">
                        <!-- Booking Card -->
                        <div class="bg-white rounded-2xl shadow-lg p-6 mb-6" data-aos="fade-left">
                            <div class="text-center mb-6">
                                <div class="text-3xl font-bold text-primary mb-2">{{ $event->formatted_price }}</div>
                                @if($event->discount_percentage)
                                    <div class="text-lg text-gray-500 line-through">
                                        Rp {{ number_format($event->original_price, 0, ',', '.') }}
                                    </div>
                                    <div class="text-sm text-green-600 font-semibold">
                                        Hemat {{ $event->discount_percentage }}%
                                    </div>
                                @endif
                            </div>

                            <!-- Availability Info -->
                            <div class="mb-6">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-gray-600">Tiket Tersedia</span>
                                    <span class="font-semibold">{{ $availableTickets }} / {{ $event->total_capacity }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-primary h-2 rounded-full transition-all duration-300"
                                         style="width: {{ $availabilityPercentage }}%"></div>
                                </div>
                                @if($availabilityPercentage <= 20)
                                    <p class="text-orange-600 text-sm mt-2 font-semibold">⚠️ Tiket hampir habis!</p>
                                @endif
                            </div>

                            @if($event->canPurchaseTickets())
                                <!-- Quantity Selector -->
                                <div class="mb-6" x-data="{ quantity: 1 }">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Jumlah Tiket</label>
                                    <div class="flex items-center justify-between border border-gray-300 rounded-lg">
                                        <button @click="quantity = Math.max(1, quantity - 1)"
                                                class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                                            </svg>
                                        </button>
                                        <span x-text="quantity" class="font-semibold text-lg"></span>
                                        <button @click="quantity = Math.min({{ min($event->max_purchase ?? 10, $availableTickets) }}, quantity + 1)"
                                                class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">
                                        Maksimal {{ min($event->max_purchase ?? 10, $availableTickets) }} tiket per pembelian
                                    </p>
                                </div>

                                <!-- Purchase Button -->
                                @auth
                                    @if(auth()->user()->role === 'pembeli')
                                        <button onclick="purchaseTicket()"
                                                class="w-full bg-gradient-to-r from-primary to-secondary text-white py-4 rounded-xl font-bold text-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                                            Beli Tiket Sekarang
                                        </button>
                                    @else
                                        <div class="w-full bg-gray-300 text-gray-600 py-4 rounded-xl font-bold text-lg text-center cursor-not-allowed">
                                            Hanya pembeli yang dapat membeli tiket
                                        </div>
                                    @endif
                                @else
                                    <a href="{{ route('login') }}"
                                       class="block w-full bg-gradient-to-r from-primary to-secondary text-white py-4 rounded-xl font-bold text-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105 text-center">
                                        Login untuk Beli Tiket
                                    </a>
                                @endauth
                            @else
                                <!-- Cannot Purchase -->
                                <div class="text-center">
                                    @if($event->availability_status == 'sold_out')
                                        <button disabled class="w-full bg-gray-400 text-white py-4 rounded-xl font-bold text-lg cursor-not-allowed">
                                            Tiket Habis
                                        </button>
                                    @elseif($event->availability_status == 'sale_not_active')
                                        <button disabled class="w-full bg-gray-400 text-white py-4 rounded-xl font-bold text-lg cursor-not-allowed">
                                            Penjualan Belum Dimulai
                                        </button>
                                    @elseif($event->hasStarted())
                                        <button disabled class="w-full bg-gray-400 text-white py-4 rounded-xl font-bold text-lg cursor-not-allowed">
                                            Event Sudah Dimulai
                                        </button>
                                    @endif
                                </div>
                            @endif

                            <!-- Security Info -->
                            <div class="mt-6 pt-6 border-t border-gray-200">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                    </svg>
                                    Pembayaran aman & terpercaya
                                </div>
                            </div>
                        </div>

                        <!-- Organizer Info -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-left" data-aos-delay="100">
                            <h3 class="text-lg font-bold text-gray-900 mb-4">Tentang Organizer</h3>
                            <div class="flex items-center space-x-4 mb-4">
                                <img src="{{ $event->organizer->avatar_url }}"
                                     alt="{{ $event->organizer->name }}"
                                     class="w-12 h-12 rounded-full object-cover">
                                <div>
                                    <h4 class="font-semibold text-gray-900">{{ $event->organizer->name }}</h4>
                                    <p class="text-sm text-gray-600">Event Organizer</p>
                                </div>
                            </div>
                            @if($organizerTickets->count() > 0)
                                <p class="text-sm text-gray-600 mb-4">
                                    Telah mengorganisir {{ $organizerTickets->count() }} event lainnya
                                </p>
                                <a href="#" class="text-primary hover:text-accent font-semibold text-sm transition-colors duration-200">
                                    Lihat Event Lainnya →
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Similar Tickets -->
    @if($similarTickets->count() > 0)
        <section class="py-16 bg-white/50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center" data-aos="fade-up">
                    Event Serupa
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($similarTickets as $similarEvent)
                        @include('components.event-card', ['event' => $similarEvent, 'delay' => $loop->index * 100])
                    @endforeach
                </div>
            </div>
        </section>
    @endif
</div>
@endsection

@push('scripts')
<script>
// Wishlist functionality
async function toggleWishlist(eventId) {
    const button = document.querySelector(`[data-event-id="${eventId}"]`);
    const icon = button.querySelector('svg');
    const isInWishlist = button.dataset.inWishlist === 'true';

    // Show loading state
    button.disabled = true;
    button.classList.add('animate-pulse');

    try {
        const response = await fetch(`/tickets/${eventId}/wishlist`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (response.ok) {
            // Update button state
            button.dataset.inWishlist = data.isInWishlist;
            button.title = data.isInWishlist ? 'Hapus dari wishlist' : 'Tambah ke wishlist';

            // Update icon appearance
            if (data.isInWishlist) {
                icon.classList.add('text-red-500', 'fill-current');
                icon.classList.remove('text-gray-600');
                icon.setAttribute('fill', 'currentColor');
            } else {
                icon.classList.remove('text-red-500', 'fill-current');
                icon.classList.add('text-gray-600');
                icon.setAttribute('fill', 'none');
            }

            // Show success animation
            button.classList.add('animate-bounce');
            setTimeout(() => button.classList.remove('animate-bounce'), 600);

            // Show notification
            window.showNotification(data.message, 'success');
        } else {
            if (response.status === 401) {
                window.showNotification('Silakan login terlebih dahulu', 'error');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 1500);
            } else {
                window.showNotification(data.error || 'Terjadi kesalahan', 'error');
            }
        }
    } catch (error) {
        console.error('Wishlist error:', error);
        window.showNotification('Terjadi kesalahan jaringan', 'error');
    } finally {
        button.disabled = false;
        button.classList.remove('animate-pulse');
    }
}

// Share functionality
function toggleShareMenu() {
    const shareMenu = document.getElementById('shareMenu');
    shareMenu.classList.toggle('hidden');

    // Close menu when clicking outside
    document.addEventListener('click', function closeMenu(e) {
        if (!e.target.closest('#shareButton') && !e.target.closest('#shareMenu')) {
            shareMenu.classList.add('hidden');
            document.removeEventListener('click', closeMenu);
        }
    });
}

function shareToWhatsApp() {
    const title = encodeURIComponent('{{ $event->title }}');
    const text = encodeURIComponent('Lihat event menarik ini: {{ $event->title }} di {{ $event->venue_name }}, {{ $event->city }}');
    const url = encodeURIComponent(window.location.href);
    const whatsappUrl = `https://wa.me/?text=${text}%20${url}`;

    window.open(whatsappUrl, '_blank');
    document.getElementById('shareMenu').classList.add('hidden');
}

function shareToFacebook() {
    const url = encodeURIComponent(window.location.href);
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;

    window.open(facebookUrl, '_blank', 'width=600,height=400');
    document.getElementById('shareMenu').classList.add('hidden');
}

function shareToTwitter() {
    const title = encodeURIComponent('{{ $event->title }}');
    const text = encodeURIComponent('Jangan lewatkan event {{ $event->title }} di {{ $event->venue_name }}, {{ $event->city }}!');
    const url = encodeURIComponent(window.location.href);
    const twitterUrl = `https://twitter.com/intent/tweet?text=${text}&url=${url}`;

    window.open(twitterUrl, '_blank', 'width=600,height=400');
    document.getElementById('shareMenu').classList.add('hidden');
}

async function copyEventLink() {
    try {
        await navigator.clipboard.writeText(window.location.href);
        window.showNotification('Link event berhasil disalin!', 'success');
    } catch (err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = window.location.href;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        window.showNotification('Link event berhasil disalin!', 'success');
    }
    document.getElementById('shareMenu').classList.add('hidden');
}

// Native Web Share API fallback
function shareEvent() {
    if (navigator.share) {
        navigator.share({
            title: '{{ $event->title }}',
            text: 'Lihat event menarik ini: {{ Str::limit($event->description, 100) }}',
            url: window.location.href
        }).catch(err => {
            console.log('Error sharing:', err);
        });
    } else {
        toggleShareMenu();
    }
}

function openMaps() {
    const address = encodeURIComponent('{{ $event->venue_address }}, {{ $event->city }}');
    window.open(`https://maps.google.com/maps?q=${address}`, '_blank');
}

function openGallery(imageUrl) {
    // Simple lightbox implementation
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
    overlay.innerHTML = `
        <div class="relative max-w-4xl max-h-full p-4">
            <img src="${imageUrl}" class="max-w-full max-h-full object-contain">
            <button onclick="this.parentElement.parentElement.remove()"
                    class="absolute top-4 right-4 text-white hover:text-gray-300">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
    `;
    document.body.appendChild(overlay);
}

function purchaseTicket() {
    // Redirect to purchase flow
    window.location.href = '{{ route("tickets.purchase", $event) }}';
}
</script>
@endpush
