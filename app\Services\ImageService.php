<?php

namespace App\Services;

use App\Facades\Image;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Exception;

class ImageService
{
    /**
     * Upload and process image with specified dimensions
     */
    public function uploadImage(
        UploadedFile $file, 
        string $directory, 
        int $maxWidth = 1200, 
        int $maxHeight = 800, 
        int $quality = 85,
        string $prefix = 'img'
    ): string {
        try {
            // Generate unique filename
            $extension = $file->getClientOriginalExtension();
            $filename = "{$prefix}_" . time() . "_" . Str::random(10) . ".{$extension}";
            $path = "{$directory}/{$filename}";

            // Process image
            $image = Image::read($file)
                ->scaleDown($maxWidth, $maxHeight);

            // Convert to appropriate format based on extension
            $processedImage = match(strtolower($extension)) {
                'png' => $image->toPng(),
                'webp' => $image->toWebp($quality),
                'gif' => $image->toGif(),
                default => $image->toJpeg($quality)
            };

            // Store the processed image
            Storage::disk('public')->put($path, $processedImage);

            return $path;
        } catch (Exception $e) {
            throw new Exception("Failed to upload image: " . $e->getMessage());
        }
    }

    /**
     * Upload poster image with predefined settings
     */
    public function uploadPoster(UploadedFile $file): string
    {
        return $this->uploadImage(
            file: $file,
            directory: 'tickets/posters',
            maxWidth: config('image.sizes.poster.width', 800),
            maxHeight: config('image.sizes.poster.height', 600),
            quality: config('image.quality.jpeg', 85),
            prefix: 'poster'
        );
    }

    /**
     * Upload gallery image with predefined settings
     */
    public function uploadGallery(UploadedFile $file): string
    {
        return $this->uploadImage(
            file: $file,
            directory: 'tickets/gallery',
            maxWidth: config('image.sizes.gallery.width', 1200),
            maxHeight: config('image.sizes.gallery.height', 800),
            quality: config('image.quality.jpeg', 85),
            prefix: 'gallery'
        );
    }

    /**
     * Upload avatar image with predefined settings
     */
    public function uploadAvatar(UploadedFile $file): string
    {
        return $this->uploadImage(
            file: $file,
            directory: 'avatars',
            maxWidth: config('image.sizes.avatar.width', 200),
            maxHeight: config('image.sizes.avatar.height', 200),
            quality: config('image.quality.jpeg', 90),
            prefix: 'avatar'
        );
    }

    /**
     * Upload thumbnail with predefined settings
     */
    public function uploadThumbnail(UploadedFile $file): string
    {
        return $this->uploadImage(
            file: $file,
            directory: 'thumbnails',
            maxWidth: config('image.sizes.thumbnail.width', 150),
            maxHeight: config('image.sizes.thumbnail.height', 150),
            quality: config('image.quality.jpeg', 80),
            prefix: 'thumb'
        );
    }

    /**
     * Create multiple sizes of an image
     */
    public function createMultipleSizes(UploadedFile $file, string $directory, array $sizes): array
    {
        $paths = [];
        
        foreach ($sizes as $sizeName => $dimensions) {
            try {
                $path = $this->uploadImage(
                    file: $file,
                    directory: "{$directory}/{$sizeName}",
                    maxWidth: $dimensions['width'],
                    maxHeight: $dimensions['height'],
                    quality: $dimensions['quality'] ?? 85,
                    prefix: $sizeName
                );
                $paths[$sizeName] = $path;
            } catch (Exception $e) {
                // Log error but continue with other sizes
                \Log::error("Failed to create {$sizeName} size: " . $e->getMessage());
            }
        }

        return $paths;
    }

    /**
     * Delete image file
     */
    public function deleteImage(string $path): bool
    {
        try {
            if (Storage::disk('public')->exists($path)) {
                return Storage::disk('public')->delete($path);
            }
            return true;
        } catch (Exception $e) {
            \Log::error("Failed to delete image: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get image URL
     */
    public function getImageUrl(string $path): string
    {
        if (filter_var($path, FILTER_VALIDATE_URL)) {
            return $path; // Already a full URL
        }

        return Storage::disk('public')->url($path);
    }

    /**
     * Validate image file
     */
    public function validateImage(UploadedFile $file, array $rules = []): array
    {
        $defaultRules = [
            'max_size' => config('image.limits.max_file_size', 5120), // KB
            'allowed_types' => ['jpeg', 'jpg', 'png', 'gif', 'webp'],
            'max_width' => config('image.limits.max_width', 2048),
            'max_height' => config('image.limits.max_height', 2048),
        ];

        $rules = array_merge($defaultRules, $rules);
        $errors = [];

        // Check file size
        if ($file->getSize() > ($rules['max_size'] * 1024)) {
            $errors[] = "File size exceeds maximum allowed size of {$rules['max_size']}KB";
        }

        // Check file type
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $rules['allowed_types'])) {
            $errors[] = "File type '{$extension}' is not allowed. Allowed types: " . implode(', ', $rules['allowed_types']);
        }

        // Check image dimensions
        try {
            $imageInfo = getimagesize($file->getPathname());
            if ($imageInfo) {
                [$width, $height] = $imageInfo;
                
                if ($width > $rules['max_width']) {
                    $errors[] = "Image width ({$width}px) exceeds maximum allowed width of {$rules['max_width']}px";
                }
                
                if ($height > $rules['max_height']) {
                    $errors[] = "Image height ({$height}px) exceeds maximum allowed height of {$rules['max_height']}px";
                }
            }
        } catch (Exception $e) {
            $errors[] = "Unable to read image dimensions";
        }

        return $errors;
    }

    /**
     * Add watermark to image
     */
    public function addWatermark(string $imagePath, string $watermarkPath = null): string
    {
        try {
            $watermarkPath = $watermarkPath ?? config('image.watermark.path');
            
            if (!file_exists($watermarkPath)) {
                throw new Exception("Watermark file not found");
            }

            $image = Image::read(Storage::disk('public')->path($imagePath));
            $watermark = Image::read($watermarkPath);

            // Resize watermark to 20% of image width
            $watermarkWidth = (int)($image->width() * 0.2);
            $watermark->scaleDown($watermarkWidth);

            // Position watermark (bottom-right with margin)
            $margin = config('image.watermark.margin', 10);
            $x = $image->width() - $watermark->width() - $margin;
            $y = $image->height() - $watermark->height() - $margin;

            // Apply watermark with opacity
            $image->place($watermark, 'bottom-right', $margin, $margin);

            // Save watermarked image
            $watermarkedPath = str_replace('.', '_watermarked.', $imagePath);
            Storage::disk('public')->put($watermarkedPath, $image->toJpeg(90));

            return $watermarkedPath;
        } catch (Exception $e) {
            throw new Exception("Failed to add watermark: " . $e->getMessage());
        }
    }

    /**
     * Get image information
     */
    public function getImageInfo(string $path): array
    {
        try {
            $fullPath = Storage::disk('public')->path($path);
            
            if (!file_exists($fullPath)) {
                throw new Exception("Image file not found");
            }

            $imageInfo = getimagesize($fullPath);
            $fileSize = filesize($fullPath);

            return [
                'width' => $imageInfo[0] ?? null,
                'height' => $imageInfo[1] ?? null,
                'type' => $imageInfo[2] ?? null,
                'mime' => $imageInfo['mime'] ?? null,
                'size' => $fileSize,
                'size_formatted' => $this->formatBytes($fileSize),
                'url' => $this->getImageUrl($path),
            ];
        } catch (Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
