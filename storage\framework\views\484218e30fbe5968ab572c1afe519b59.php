<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Dashboard Header -->
    <div class="mb-8" data-aos="fade-up">
        <h1 class="text-2xl font-bold mb-2">Dashboard Organizer</h1>
        <p class="text-gray-600">Kelola event dan pantau performa penjualan Anda</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Tickets -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-primary/10 rounded-lg">
                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Total Event</p>
                    <p class="text-2xl font-bold"><?php echo e($stats['total_tickets']); ?></p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-green-600 font-medium"><?php echo e($stats['published_tickets']); ?> Published</span>
                <span class="text-gray-400 mx-2">•</span>
                <span class="text-orange-600"><?php echo e($stats['draft_tickets']); ?> Draft</span>
            </div>
        </div>

        <!-- Total Revenue -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="200">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Total Revenue</p>
                    <p class="text-2xl font-bold">Rp <?php echo e(number_format($stats['total_revenue'], 0, ',', '.')); ?></p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-green-600 font-medium">
                    +Rp <?php echo e(number_format($stats['period_revenue'], 0, ',', '.')); ?>

                </span>
                <span class="text-gray-400 mx-2">•</span>
                <span class="text-gray-600"><?php echo e($dateRange); ?> hari terakhir</span>
            </div>
        </div>

        <!-- Tickets Sold -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="300">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Tiket Terjual</p>
                    <p class="text-2xl font-bold"><?php echo e(number_format($stats['total_tickets_sold'], 0, ',', '.')); ?></p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-blue-600 font-medium">
                    +<?php echo e(number_format($stats['period_tickets_sold'], 0, ',', '.')); ?>

                </span>
                <span class="text-gray-400 mx-2">•</span>
                <span class="text-gray-600"><?php echo e($dateRange); ?> hari terakhir</span>
            </div>
        </div>

        <!-- Total Customers -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="400">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Total Pelanggan</p>
                    <p class="text-2xl font-bold"><?php echo e(number_format($stats['total_customers'], 0, ',', '.')); ?></p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-purple-600 font-medium">
                    +<?php echo e(number_format($stats['period_customers'], 0, ',', '.')); ?>

                </span>
                <span class="text-gray-400 mx-2">•</span>
                <span class="text-gray-600"><?php echo e($dateRange); ?> hari terakhir</span>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Revenue Chart -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="500">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">Revenue Harian</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-primary text-white rounded-lg">7 Hari</button>
                    <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">30 Hari</button>
                </div>
            </div>
            <div class="h-64 flex items-center justify-center text-gray-500">
                <div class="text-center">
                    <svg class="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                    <p>Chart akan ditampilkan di sini</p>
                </div>
            </div>
        </div>

        <!-- Event Performance -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="600">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">Performa Event</h2>
                <a href="<?php echo e(route('organizer.tickets.index')); ?>" class="text-primary hover:text-primary/80 transition-colors">
                    Lihat Semua
                </a>
            </div>
            <div class="space-y-4">
                <?php $__currentLoopData = $eventPerformance->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-900"><?php echo e($event['title']); ?></h3>
                        <p class="text-sm text-gray-600">
                            <?php echo e($event['sold_tickets']); ?>/<?php echo e($event['total_capacity']); ?> tiket terjual
                        </p>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-900">
                            Rp <?php echo e(number_format($event['revenue'], 0, ',', '.')); ?>

                        </p>
                        <p class="text-sm text-gray-600"><?php echo e($event['conversion_rate']); ?>%</p>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Recent Activities & Upcoming Tickets -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Activities -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="700">
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-6">Aktivitas Terbaru</h2>
                <div class="space-y-4">
                    <?php $__currentLoopData = $recentActivities->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-start space-x-4">
                        <div class="p-2 bg-primary/10 rounded-lg">
                            <?php if($activity['type'] === 'order'): ?>
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                </svg>
                            <?php else: ?>
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            <?php endif; ?>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium text-gray-900"><?php echo e($activity['title']); ?></h3>
                            <p class="text-sm text-gray-600"><?php echo e($activity['description']); ?></p>
                            <p class="text-xs text-gray-500 mt-1">
                                <?php echo e($activity['created_at']->diffForHumans()); ?>

                            </p>
                        </div>
                        <?php if(isset($activity['amount'])): ?>
                        <div class="text-right">
                            <p class="text-sm font-medium text-green-600">
                                +Rp <?php echo e(number_format($activity['amount'], 0, ',', '.')); ?>

                            </p>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- Upcoming Tickets -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="800">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-semibold">Event Mendatang</h2>
                    <a href="<?php echo e(route('organizer.tickets.create')); ?>" class="text-primary hover:text-primary/80 transition-colors">
                        Buat Event
                    </a>
                </div>
                <div class="space-y-4">
                    <?php $__empty_1 = true; $__currentLoopData = $upcomingTickets->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                            <span class="text-primary font-semibold text-sm">
                                <?php echo e(\Carbon\Carbon::parse($event->start_date)->format('d')); ?>

                            </span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium truncate"><?php echo e($event->title); ?></h3>
                            <p class="text-sm text-gray-600">
                                <?php echo e(\Carbon\Carbon::parse($event->start_date)->format('d M Y, H:i')); ?>

                            </p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                <?php echo e($event->status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                                <?php echo e(ucfirst($event->status)); ?>

                            </span>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        <p class="text-gray-600">Belum ada event mendatang</p>
                        <a href="<?php echo e(route('organizer.tickets.create')); ?>" class="text-primary hover:text-primary/80 transition-colors">
                            Buat event pertama Anda
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/pages/organizer/dashboard.blade.php ENDPATH**/ ?>