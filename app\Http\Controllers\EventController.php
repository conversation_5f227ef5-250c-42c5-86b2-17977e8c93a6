<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Carbon\Carbon;

class EventController extends Controller
{
    /**
     * Display tickets listing with advanced filtering
     */
    public function index(Request $request)
    {
        $query = Event::with(['category', 'organizer'])
            ->where('status', 'published')
            ->where('start_date', '>', now());

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('venue_name', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Location filter
        if ($request->filled('city')) {
            $query->where('city', 'like', "%{$request->city}%");
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->where('start_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('start_date', '<=', $request->date_to);
        }

        // Price range filter
        if ($request->filled('price_min')) {
            $query->where('price', '>=', $request->price_min);
        }
        if ($request->filled('price_max')) {
            $query->where('price', '<=', $request->price_max);
        }

        // Free tickets filter
        if ($request->boolean('free_only')) {
            $query->where('is_free', true);
        }

        // Sorting
        $sortBy = $request->get('sort', 'start_date');
        $sortOrder = $request->get('order', 'asc');

        switch ($sortBy) {
            case 'price':
                $query->orderBy('price', $sortOrder);
                break;
            case 'popularity':
                $query->withCount('tickets')
                      ->orderBy('tickets_count', 'desc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            default:
                $query->orderBy('start_date', $sortOrder);
        }

        // Pagination
        $tickets = $query->paginate(12)->withQueryString();

        // Get categories for filter
        $categories = Cache::remember('categories_list', 3600, function () {
            return Category::orderBy('name')->get();
        });

        // Get popular cities
        $popularCities = Cache::remember('popular_cities', 3600, function () {
            return Event::select('city', DB::raw('count(*) as total'))
                ->where('status', 'published')
                ->groupBy('city')
                ->orderBy('total', 'desc')
                ->limit(10)
                ->pluck('city');
        });

        return view('tickets.index', compact('tickets', 'categories', 'popularCities'));
    }

    /**
     * Display event detail with recommendations
     */
    public function show(Event $event)
    {
        // Check if event is published
        if ($event->status !== 'published') {
            abort(404);
        }

        // Load relationships
        $event->load(['category', 'organizer', 'tickets']);

        // Increment view count
        $event->increment('view_count');

        // Get similar tickets
        $similarTickets = Event::where('category_id', $event->category_id)
            ->where('id', '!=', $event->id)
            ->where('status', 'published')
            ->where('start_date', '>', now())
            ->limit(4)
            ->get();

        // Get organizer's other tickets
        $organizerTickets = Event::where('organizer_id', $event->organizer_id)
            ->where('id', '!=', $event->id)
            ->where('status', 'published')
            ->where('start_date', '>', now())
            ->limit(3)
            ->get();

        // Check if user has tickets for this event
        $userHasTickets = false;
        if (auth()->check()) {
            $userHasTickets = $event->tickets()
                ->where('buyer_id', auth()->id())
                ->where('status', 'active')
                ->exists();
        }

        // Calculate availability
        $soldTickets = $event->tickets()->where('status', '!=', 'cancelled')->count();
        $availableTickets = $event->total_capacity - $soldTickets;
        $availabilityPercentage = ($availableTickets / $event->total_capacity) * 100;

        return view('events.show', compact(
            'event',
            'similarTickets',
            'organizerTickets',
            'userHasTickets',
            'availableTickets',
            'availabilityPercentage'
        ));
    }

    /**
     * Search tickets with autocomplete
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $tickets = Event::where('status', 'published')
            ->where('start_date', '>', now())
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('venue_name', 'like', "%{$query}%")
                  ->orWhere('city', 'like', "%{$query}%");
            })
            ->select('id', 'title', 'venue_name', 'city', 'start_date', 'poster', 'price')
            ->limit(10)
            ->get()
            ->map(function ($event) {
                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'venue' => $event->venue_name,
                    'city' => $event->city,
                    'date' => $event->start_date->format('d M Y'),
                    'poster' => $event->poster_url,
                    'price' => $event->formatted_price,
                    'url' => route('tickets.show', $event)
                ];
            });

        return response()->json($tickets);
    }

    /**
     * Get tickets by category
     */
    public function byCategory(Category $category, Request $request)
    {
        $query = $category->tickets()
            ->where('status', 'published')
            ->where('start_date', '>', now())
            ->with(['organizer']);

        // Apply additional filters if provided
        if ($request->filled('city')) {
            $query->where('city', 'like', "%{$request->city}%");
        }

        if ($request->filled('price_range')) {
            switch ($request->price_range) {
                case 'free':
                    $query->where('is_free', true);
                    break;
                case 'under_100k':
                    $query->where('price', '<', 100000);
                    break;
                case '100k_500k':
                    $query->whereBetween('price', [100000, 500000]);
                    break;
                case 'above_500k':
                    $query->where('price', '>', 500000);
                    break;
            }
        }

        $tickets = $query->orderBy('start_date')
            ->paginate(12)
            ->withQueryString();

        return view('tickets.category', compact('category', 'tickets'));
    }

    /**
     * Get featured tickets for homepage
     */
    public function featured()
    {
        $featuredTickets = Cache::remember('featured_Tickets', 1800, function () {
            return Event::where('status', 'published')
                ->where('is_featured', true)
                ->where('start_date', '>', now())
                ->with(['category', 'organizer'])
                ->orderBy('start_date')
                ->limit(6)
                ->get();
        });

        return response()->json($featuredTickets->map(function ($event) {
            return [
                'id' => $event->id,
                'title' => $event->title,
                'description' => Str::limit($event->description, 100),
                'venue_name' => $event->venue_name,
                'city' => $event->city,
                'start_date' => $event->start_date->format('d M Y'),
                'price' => $event->formatted_price,
                'poster' => $event->poster_url,
                'category' => $event->category->name,
                'organizer' => $event->organizer->name,
                'url' => route('tickets.show', $event),
                'is_free' => $event->is_free,
                'available_tickets' => $event->available_capacity
            ];
        }));
    }

    /**
     * Get upcoming tickets
     */
    public function upcoming(Request $request)
    {
        $limit = $request->get('limit', 10);

        $tickets = Event::where('status', 'published')
            ->where('start_date', '>', now())
            ->where('start_date', '<=', now()->addDays(30))
            ->with(['category', 'organizer'])
            ->orderBy('start_date')
            ->limit($limit)
            ->get();

        return response()->json($tickets);
    }

    /**
     * Get tickets near user location
     */
    public function nearby(Request $request)
    {
        $latitude = $request->get('lat');
        $longitude = $request->get('lng');
        $radius = $request->get('radius', 50); // km

        if (!$latitude || !$longitude) {
            return response()->json(['error' => 'Location required'], 400);
        }

        $tickets = Event::select('*')
            ->selectRaw("
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) *
                cos(radians(longitude) - radians(?)) + sin(radians(?)) *
                sin(radians(latitude)))) AS distance
            ", [$latitude, $longitude, $latitude])
            ->where('status', 'published')
            ->where('start_date', '>', now())
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->having('distance', '<', $radius)
            ->orderBy('distance')
            ->with(['category', 'organizer'])
            ->limit(20)
            ->get();

        return response()->json($tickets);
    }

    /**
     * Add event to wishlist
     */
    public function addToWishlist(Event $event)
    {
        if (!auth()->check()) {
            return response()->json(['error' => 'Authentication required'], 401);
        }

        $user = auth()->user();

        if ($user->wishlist()->where('tiket_id', $event->id)->exists()) {
            return response()->json(['message' => 'Event already in wishlist'], 200);
        }

        $user->wishlist()->attach($event->id);

        return response()->json(['message' => 'Event added to wishlist'], 201);
    }

    /**
     * Remove event from wishlist
     */
    public function removeFromWishlist(Event $event)
    {
        if (!auth()->check()) {
            return response()->json(['error' => 'Authentication required'], 401);
        }

        auth()->user()->wishlist()->detach($event->id);

        return response()->json(['message' => 'Event removed from wishlist'], 200);
    }
}
