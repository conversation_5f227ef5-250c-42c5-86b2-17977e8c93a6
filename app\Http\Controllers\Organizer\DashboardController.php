<?php

namespace App\Http\Controllers\Organizer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Order;
use App\Models\Ticket;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'organizer']);
    }

    /**
     * Show organizer dashboard
     */
    public function index(Request $request)
    {
        $organizer = auth()->user();
        $dateRange = $request->get('range', '30'); // Default 30 days
        $startDate = Carbon::now()->subDays($dateRange);

        // Basic Statistics
        $stats = $this->getBasicStats($organizer, $startDate);

        // Revenue Analytics
        $revenueData = $this->getRevenueAnalytics($organizer, $startDate);

        // Event Performance
        $eventPerformance = $this->getEventPerformance($organizer, $startDate);

        // Recent Activities
        $recentActivities = $this->getRecentActivities($organizer);

        // Upcoming Tickets
        $upcomingTickets = $this->getUpcomingTickets($organizer);

        // Top Performing Tickets
        $topTickets = $this->getTopPerformingTickets($organizer, $startDate);

        // Customer Analytics
        $customerAnalytics = $this->getCustomerAnalytics($organizer, $startDate);

        // Payment Method Analytics
        $paymentAnalytics = $this->getPaymentMethodAnalytics($organizer, $startDate);

        return view('pages.organizer.dashboard', compact(
            'stats',
            'revenueData',
            'eventPerformance',
            'recentActivities',
            'upcomingTickets',
            'topTickets',
            'customerAnalytics',
            'paymentAnalytics',
            'dateRange'
        ));
    }

    /**
     * Get basic statistics
     */
    private function getBasicStats($organizer, $startDate)
    {
        // Get events (tickets in this context means events)
        $events = Event::where('organizer_id', $organizer->id);

        // Get orders for this organizer
        $orders = Order::whereHas('event', function($q) use ($organizer) {
            $q->where('organizer_id', $organizer->id);
        });

        // Get actual tickets (purchased tickets)
        $purchasedTickets = Ticket::whereHas('event', function($q) use ($organizer) {
            $q->where('organizer_id', $organizer->id);
        });

        return [
            'total_tickets' => $events->count(), // Total events created
            'total' => $events->count(), // Alias for compatibility
            'published_tickets' => $events->where('status', 'published')->count(),
            'draft_tickets' => $events->where('status', 'draft')->count(),
            'total_revenue' => $orders->where('payment_status', 'paid')->sum('total_amount'),
            'period_revenue' => $orders->where('payment_status', 'paid')
                ->where('created_at', '>=', $startDate)->sum('total_amount'),
            'total_tickets_sold' => $purchasedTickets->where('status', '!=', 'cancelled')->count(),
            'period_tickets_sold' => $purchasedTickets->where('status', '!=', 'cancelled')
                ->where('created_at', '>=', $startDate)->count(),
            'total_customers' => $orders->distinct('user_id')->count(),
            'period_customers' => $orders->where('created_at', '>=', $startDate)
                ->distinct('user_id')->count(),
            'avg_ticket_price' => $purchasedTickets->where('status', '!=', 'cancelled')->avg('price') ?? 0,
            'conversion_rate' => $this->calculateConversionRate($organizer, $startDate),
        ];
    }

    /**
     * Get revenue analytics data
     */
    private function getRevenueAnalytics($organizer, $startDate)
    {
        $dailyRevenue = Order::whereHas('event', function($q) use ($organizer) {
            $q->where('organizer_id', $organizer->id);
        })
        ->where('payment_status', 'paid')
        ->where('created_at', '>=', $startDate)
        ->select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('SUM(total_amount) as revenue'),
            DB::raw('COUNT(*) as orders'),
            DB::raw('SUM(quantity) as tickets')
        )
        ->groupBy('date')
        ->orderBy('date')
        ->get();

        $monthlyRevenue = Order::whereHas('event', function($q) use ($organizer) {
            $q->where('organizer_id', $organizer->id);
        })
        ->where('payment_status', 'paid')
        ->where('created_at', '>=', Carbon::now()->subMonths(12))
        ->select(
            DB::raw('YEAR(created_at) as year'),
            DB::raw('MONTH(created_at) as month'),
            DB::raw('SUM(total_amount) as revenue'),
            DB::raw('COUNT(*) as orders')
        )
        ->groupBy('year', 'month')
        ->orderBy('year')
        ->orderBy('month')
        ->get();

        return [
            'daily' => $dailyRevenue,
            'monthly' => $monthlyRevenue,
            'growth_rate' => $this->calculateGrowthRate($organizer, $startDate),
        ];
    }

    /**
     * Get event performance data
     */
    private function getEventPerformance($organizer, $startDate)
    {
        return Event::where('organizer_id', $organizer->id)
            ->where('created_at', '>=', $startDate)
            ->withCount(['orders', 'tickets'])
            ->with(['orders' => function($q) {
                $q->where('payment_status', 'paid');
            }])
            ->get()
            ->map(function($event) {
                $soldTickets = $event->tickets()->where('status', '!=', 'cancelled')->count();
                $revenue = $event->orders()->where('payment_status', 'paid')->sum('total_amount');

                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'start_date' => $event->start_date,
                    'total_capacity' => $event->total_capacity,
                    'sold_tickets' => $soldTickets,
                    'available_tickets' => $event->available_capacity,
                    'revenue' => $revenue,
                    'conversion_rate' => $event->total_capacity > 0 ?
                        round(($soldTickets / $event->total_capacity) * 100, 2) : 0,
                    'avg_ticket_price' => $soldTickets > 0 ? round($revenue / $soldTickets, 0) : 0,
                    'status' => $event->status,
                ];
            });
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities($organizer)
    {
        $activities = collect();

        // Recent orders
        $recentOrders = Order::whereHas('event', function($q) use ($organizer) {
            $q->where('organizer_id', $organizer->id);
        })
        ->with(['event', 'user'])
        ->latest()
        ->take(10)
        ->get()
        ->map(function($order) {
            return [
                'type' => 'order',
                'title' => "Pesanan baru untuk {$order->event->title}",
                'description' => "{$order->user->name} membeli {$order->quantity} tiket",
                'amount' => $order->total_amount,
                'created_at' => $order->created_at,
                'status' => $order->payment_status,
            ];
        });

        // Recent ticket validations
        $recentValidations = Ticket::whereHas('event', function($q) use ($organizer) {
            $q->where('organizer_id', $organizer->id);
        })
        ->where('status', 'used')
        ->with(['event', 'buyer'])
        ->latest('used_at')
        ->take(5)
        ->get()
        ->map(function($ticket) {
            return [
                'type' => 'validation',
                'title' => "Tiket divalidasi untuk {$ticket->event->title}",
                'description' => "Tiket {$ticket->ticket_number} telah digunakan",
                'created_at' => $ticket->used_at,
                'status' => 'used',
            ];
        });

        return $activities->merge($recentOrders)
            ->merge($recentValidations)
            ->sortByDesc('created_at')
            ->take(15)
            ->values();
    }

    /**
     * Get upcoming tickets
     */
    private function getUpcomingTickets($organizer)
    {
        return Event::where('organizer_id', $organizer->id)
            ->where('start_date', '>', now())
            ->where('status', 'published')
            ->orderBy('start_date')
            ->take(5)
            ->get()
            ->map(function($event) {
                $soldTickets = $event->tickets()->where('status', '!=', 'cancelled')->count();

                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'start_date' => $event->start_date,
                    'venue_name' => $event->venue_name,
                    'city' => $event->city,
                    'sold_tickets' => $soldTickets,
                    'total_capacity' => $event->total_capacity,
                    'revenue' => $event->orders()->where('payment_status', 'paid')->sum('total_amount'),
                    'days_until' => now()->diffInDays($event->start_date),
                ];
            });
    }

    /**
     * Get top performing tickets
     */
    private function getTopPerformingTickets($organizer, $startDate)
    {
        return Event::where('organizer_id', $organizer->id)
            ->where('created_at', '>=', $startDate)
            ->withCount(['tickets' => function($q) {
                $q->where('status', '!=', 'cancelled');
            }])
            ->with(['orders' => function($q) {
                $q->where('payment_status', 'paid');
            }])
            ->get()
            ->map(function($event) {
                $revenue = $event->orders->sum('total_amount');
                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'tickets_sold' => $event->tickets_count,
                    'revenue' => $revenue,
                    'conversion_rate' => $event->total_capacity > 0 ?
                        round(($event->tickets_count / $event->total_capacity) * 100, 2) : 0,
                ];
            })
            ->sortByDesc('revenue')
            ->take(5)
            ->values();
    }

    /**
     * Get customer analytics
     */
    private function getCustomerAnalytics($organizer, $startDate)
    {
        $customers = User::whereHas('orders.event', function($q) use ($organizer) {
            $q->where('organizer_id', $organizer->id);
        })
        ->withCount(['orders' => function($q) use ($organizer, $startDate) {
            $q->whereHas('event', function($eq) use ($organizer) {
                $eq->where('organizer_id', $organizer->id);
            })
            ->where('created_at', '>=', $startDate)
            ->where('payment_status', 'paid');
        }])
        ->with(['orders' => function($q) use ($organizer, $startDate) {
            $q->whereHas('event', function($eq) use ($organizer) {
                $eq->where('organizer_id', $organizer->id);
            })
            ->where('created_at', '>=', $startDate)
            ->where('payment_status', 'paid');
        }])
        ->get();

        $topCustomers = $customers->map(function($customer) {
            $totalSpent = $customer->orders->sum('total_amount');
            return [
                'name' => $customer->name,
                'email' => $customer->email,
                'orders_count' => $customer->orders_count,
                'total_spent' => $totalSpent,
                'avg_order_value' => $customer->orders_count > 0 ?
                    round($totalSpent / $customer->orders_count, 0) : 0,
            ];
        })
        ->sortByDesc('total_spent')
        ->take(10)
        ->values();

        return [
            'top_customers' => $topCustomers,
            'new_customers' => $customers->where('created_at', '>=', $startDate)->count(),
            'repeat_customers' => $customers->where('orders_count', '>', 1)->count(),
        ];
    }

    /**
     * Get payment method analytics
     */
    private function getPaymentMethodAnalytics($organizer, $startDate)
    {
        return Order::whereHas('event', function($q) use ($organizer) {
            $q->where('organizer_id', $organizer->id);
        })
        ->where('payment_status', 'paid')
        ->where('created_at', '>=', $startDate)
        ->select('payment_method',
            DB::raw('COUNT(*) as count'),
            DB::raw('SUM(total_amount) as revenue'),
            DB::raw('AVG(total_amount) as avg_amount')
        )
        ->groupBy('payment_method')
        ->get()
        ->map(function($item) {
            return [
                'method' => $item->payment_method,
                'count' => $item->count,
                'revenue' => $item->revenue,
                'avg_amount' => round($item->avg_amount, 0),
                'percentage' => 0, // Will be calculated in view
            ];
        });
    }

    /**
     * Calculate conversion rate
     */
    private function calculateConversionRate($organizer, $startDate)
    {
        $tickets = Event::where('organizer_id', $organizer->id)
            ->where('created_at', '>=', $startDate)
            ->get();

        $totalViews = $tickets->sum('view_count');
        $totalOrders = Order::whereIn('event_id', $tickets->pluck('id'))
            ->where('payment_status', 'paid')
            ->count();

        return $totalViews > 0 ? round(($totalOrders / $totalViews) * 100, 2) : 0;
    }

    /**
     * Calculate growth rate
     */
    private function calculateGrowthRate($organizer, $startDate)
    {
        $currentPeriod = Order::whereHas('event', function($q) use ($organizer) {
            $q->where('organizer_id', $organizer->id);
        })
        ->where('payment_status', 'paid')
        ->where('created_at', '>=', $startDate)
        ->sum('total_amount');

        $previousStart = $startDate->copy()->subDays($startDate->diffInDays(now()));
        $previousPeriod = Order::whereHas('event', function($q) use ($organizer) {
            $q->where('organizer_id', $organizer->id);
        })
        ->where('payment_status', 'paid')
        ->whereBetween('created_at', [$previousStart, $startDate])
        ->sum('total_amount');

        if ($previousPeriod > 0) {
            return round((($currentPeriod - $previousPeriod) / $previousPeriod) * 100, 2);
        }

        return $currentPeriod > 0 ? 100 : 0;
    }

    /**
     * Export analytics data
     */
    public function export(Request $request)
    {
        $organizer = auth()->user();
        $format = $request->get('format', 'csv');
        $dateRange = $request->get('range', '30');
        $startDate = Carbon::now()->subDays($dateRange);

        $data = $this->getExportData($organizer, $startDate);

        if ($format === 'pdf') {
            return $this->exportToPDF($data);
        }

        return $this->exportToCSV($data);
    }

    /**
     * Get data for export
     */
    private function getExportData($organizer, $startDate)
    {
        return [
            'tickets' => $this->getEventPerformance($organizer, $startDate),
            'revenue' => $this->getRevenueAnalytics($organizer, $startDate),
            'customers' => $this->getCustomerAnalytics($organizer, $startDate),
            'payments' => $this->getPaymentMethodAnalytics($organizer, $startDate),
        ];
    }

    /**
     * Export to CSV
     */
    private function exportToCSV($data)
    {
        $filename = 'analytics-' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // Tickets data
            fputcsv($file, ['Event Analytics']);
            fputcsv($file, ['Event', 'Tickets Sold', 'Revenue', 'Conversion Rate']);

            foreach ($data['tickets'] as $event) {
                fputcsv($file, [
                    $event['title'],
                    $event['sold_tickets'],
                    $event['revenue'],
                    $event['conversion_rate'] . '%'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export to PDF
     */
    private function exportToPDF($data)
    {
        // Implementation would use DomPDF or similar
        return response()->json(['message' => 'PDF export not implemented yet']);
    }
}
