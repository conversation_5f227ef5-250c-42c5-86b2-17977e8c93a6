<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Category;
use App\Models\User;
use App\Models\Ticket;
use Illuminate\Support\Str;
use Carbon\Carbon;

class TicketController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display a listing of tickets (tickets management)
     */
    public function index(Request $request)
    {
        $query = Event::with(['organizer', 'category'])
            ->withCount('tickets');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('venue_name', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Status filter
        if ($request->filled('status')) {
            $status = $request->status;
            $now = Carbon::now();
            
            switch ($status) {
                case 'upcoming':
                    $query->where('start_date', '>', $now);
                    break;
                case 'ongoing':
                    $query->where('start_date', '<=', $now)
                          ->where('end_date', '>=', $now);
                    break;
                case 'completed':
                    $query->where('end_date', '<', $now);
                    break;
            }
        }

        // Sorting
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'title':
                $query->orderBy('title');
                break;
            case 'date':
                $query->orderBy('start_date');
                break;
            case 'price':
                $query->orderBy('price');
                break;
            default:
                $query->latest();
        }

        $tickets = $query->paginate(10)->withQueryString();
        $categories = Category::all();

        return view('pages.admin.tickets', compact('tickets', 'categories'));
    }

    /**
     * Show the form for creating a new event
     */
    public function create()
    {
        $categories = Category::all();
        $organizers = User::where('role', 'penjual')->get();
        
        return view('pages.admin.tickets.create', compact('categories', 'organizers'));
    }

    /**
     * Store a newly created event
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'organizer_id' => 'required|exists:users,id',
            'venue_name' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'city' => 'required|string|max:100',
            'start_date' => 'required|date|after:now',
            'end_date' => 'required|date|after:start_date',
            'price' => 'required|numeric|min:0',
            'total_capacity' => 'required|integer|min:1',
            'poster' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
        ]);

        // Generate slug
        $validated['slug'] = Str::slug($validated['title']);
        
        // Ensure unique slug
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (Event::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Set initial values
        $validated['available_capacity'] = $validated['total_capacity'];
        $validated['status'] = 'published';
        $validated['is_featured'] = $request->boolean('is_featured');

        // Handle poster upload
        if ($request->hasFile('poster')) {
            $posterPath = $request->file('poster')->store('tickets', 'public');
            $validated['poster'] = $posterPath;
        } else {
            // Use placeholder
            $validated['poster'] = 'https://via.placeholder.com/400x600/A8D5BA/FFFFFF?text=' . urlencode($validated['title']);
        }

        $event = Event::create($validated);

        return redirect()->route('admin.tickets.index')
            ->with('success', 'Event berhasil dibuat!');
    }

    /**
     * Show the form for editing an event
     */
    public function edit(Event $event)
    {
        $categories = Category::all();
        $organizers = User::where('role', 'penjual')->get();
        
        return view('pages.admin.tickets.edit', compact('event', 'categories', 'organizers'));
    }

    /**
     * Update the specified event
     */
    public function update(Request $request, Event $event)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'organizer_id' => 'required|exists:users,id',
            'venue_name' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'city' => 'required|string|max:100',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'price' => 'required|numeric|min:0',
            'total_capacity' => 'required|integer|min:1',
            'poster' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
            'status' => 'required|in:draft,published,cancelled',
        ]);

        // Update slug if title changed
        if ($validated['title'] !== $event->title) {
            $validated['slug'] = Str::slug($validated['title']);
            
            // Ensure unique slug
            $originalSlug = $validated['slug'];
            $counter = 1;
            while (Event::where('slug', $validated['slug'])->where('id', '!=', $event->id)->exists()) {
                $validated['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        $validated['is_featured'] = $request->boolean('is_featured');

        // Handle poster upload
        if ($request->hasFile('poster')) {
            // Delete old poster if it's not a placeholder
            if ($event->poster && !str_contains($event->poster, 'placeholder')) {
                \Storage::disk('public')->delete($event->poster);
            }
            
            $posterPath = $request->file('poster')->store('tickets', 'public');
            $validated['poster'] = $posterPath;
        }

        // Update available capacity if total capacity changed
        if ($validated['total_capacity'] !== $event->total_capacity) {
            $soldTickets = $event->tickets()->where('status', 'used')->count();
            $validated['available_capacity'] = max(0, $validated['total_capacity'] - $soldTickets);
        }

        $event->update($validated);

        return redirect()->route('admin.tickets.index')
            ->with('success', 'Event berhasil diperbarui!');
    }

    /**
     * Remove the specified event
     */
    public function destroy(Event $event)
    {
        // Check if event has sold tickets
        $soldTicketsCount = $event->tickets()->where('status', 'used')->count();
        
        if ($soldTicketsCount > 0) {
            return redirect()->route('admin.tickets.index')
                ->with('error', 'Tidak dapat menghapus event yang sudah memiliki tiket terjual!');
        }

        // Delete poster if it's not a placeholder
        if ($event->poster && !str_contains($event->poster, 'placeholder')) {
            \Storage::disk('public')->delete($event->poster);
        }

        // Delete related tickets (if any)
        $event->tickets()->delete();
        
        // Delete the event
        $event->delete();

        return redirect()->route('admin.tickets.index')
            ->with('success', 'Event berhasil dihapus!');
    }

    /**
     * Show event statistics
     */
    public function show(Event $event)
    {
        $event->load(['organizer', 'category', 'tickets']);
        
        $statistics = [
            'total_tickets' => $event->tickets->count(),
            'used_tickets' => $event->tickets->where('status', 'used')->count(),
            'cancelled_tickets' => $event->tickets->where('status', 'cancelled')->count(),
            'total_revenue' => $event->tickets->where('status', 'used')->sum('price'),
            'capacity_utilization' => $event->total_capacity > 0 ? 
                ($event->tickets->where('status', 'used')->count() / $event->total_capacity) * 100 : 0,
        ];

        return view('pages.admin.tickets.show', compact('event', 'statistics'));
    }

    /**
     * Toggle event featured status
     */
    public function toggleFeatured(Event $event)
    {
        $event->update(['is_featured' => !$event->is_featured]);
        
        $status = $event->is_featured ? 'ditampilkan' : 'disembunyikan';
        
        return redirect()->back()
            ->with('success', "Event berhasil {$status} dari featured!");
    }

    /**
     * Bulk actions for tickets
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,feature,unfeature,publish,unpublish',
            'tiket_ids' => 'required|array',
            'tiket_ids.*' => 'exists:tickets,id',
        ]);

        $tickets = Event::whereIn('id', $validated['tiket_ids']);

        switch ($validated['action']) {
            case 'delete':
                // Check for sold tickets
                $ticketsWithSoldTickets = $tickets->whereHas('tickets', function ($query) {
                    $query->where('status', 'used');
                })->count();

                if ($ticketsWithSoldTickets > 0) {
                    return redirect()->back()
                        ->with('error', 'Beberapa event tidak dapat dihapus karena sudah memiliki tiket terjual!');
                }

                $tickets->delete();
                $message = 'Event terpilih berhasil dihapus!';
                break;

            case 'feature':
                $tickets->update(['is_featured' => true]);
                $message = 'Event terpilih berhasil ditampilkan di featured!';
                break;

            case 'unfeature':
                $tickets->update(['is_featured' => false]);
                $message = 'Event terpilih berhasil dihapus dari featured!';
                break;

            case 'publish':
                $tickets->update(['status' => 'published']);
                $message = 'Event terpilih berhasil dipublikasikan!';
                break;

            case 'unpublish':
                $tickets->update(['status' => 'draft']);
                $message = 'Event terpilih berhasil dijadikan draft!';
                break;
        }

        return redirect()->back()->with('success', $message);
    }
}
