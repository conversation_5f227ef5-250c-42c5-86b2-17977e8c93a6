<!-- Floating Footer Navigation (Mobile Only) -->
<nav class="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-lg border-t border-gray-200/50 md:hidden z-50 shadow-lg">
    <div class="flex justify-around items-center py-1">
        <!-- Home -->
        <a href="<?php echo e(route('home')); ?>" class="flex flex-col items-center p-3 transition-all duration-300 <?php echo e(request()->routeIs('home') ? 'text-primary scale-110' : 'text-gray-500 hover:text-primary'); ?>">
            <div class="relative">
                <svg class="w-6 h-6 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                <?php if(request()->routeIs('home')): ?>
                    <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"></div>
                <?php endif; ?>
            </div>
            <span class="text-xs mt-1 font-medium">Beranda</span>
        </a>

        <!-- Tickets -->
        <a href="<?php echo e(route('tickets.index')); ?>" class="flex flex-col items-center p-3 transition-all duration-300 <?php echo e(request()->routeIs('tickets.*') ? 'text-primary scale-110' : 'text-gray-500 hover:text-primary'); ?>">
            <div class="relative">
                <svg class="w-6 h-6 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                </svg>
                <?php if(request()->routeIs('tickets.*')): ?>
                    <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"></div>
                <?php endif; ?>
            </div>
            <span class="text-xs mt-1 font-medium">Tickets</span>
        </a>

        <!-- Center Action Button (Add Event for Organizers, Search for others) -->
        <?php if(auth()->guard()->check()): ?>
            <?php if(auth()->user()->role === 'penjual' || auth()->user()->role === 'admin'): ?>
                <a href="<?php echo e(route('organizer.tickets.create')); ?>" class="flex flex-col items-center p-2 -mt-4">
                    <div class="bg-gradient-to-r from-primary to-green-400 rounded-full p-3 shadow-lg transform hover:scale-105 transition-all duration-300">
                        <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                        </svg>
                    </div>
                    <span class="text-xs mt-1 font-medium text-primary">Tambah</span>
                </a>
            <?php else: ?>
                <a href="<?php echo e(route('tickets.search')); ?>" class="flex flex-col items-center p-2 -mt-4">
                    <div class="bg-gradient-to-r from-primary to-green-400 rounded-full p-3 shadow-lg transform hover:scale-105 transition-all duration-300">
                        <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                    </div>
                    <span class="text-xs mt-1 font-medium text-primary">Cari</span>
                </a>
            <?php endif; ?>
        <?php else: ?>
            <a href="<?php echo e(route('tickets.search')); ?>" class="flex flex-col items-center p-2 -mt-4">
                <div class="bg-gradient-to-r from-primary to-green-400 rounded-full p-3 shadow-lg transform hover:scale-105 transition-all duration-300">
                    <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                </div>
                <span class="text-xs mt-1 font-medium text-primary">Cari</span>
            </a>
        <?php endif; ?>

        <!-- My Tickets -->
        <?php if(auth()->guard()->check()): ?>
            <a href="<?php echo e(route('tickets.my-tickets')); ?>" class="flex flex-col items-center p-3 transition-all duration-300 <?php echo e(request()->routeIs('tickets.*') ? 'text-primary scale-110' : 'text-gray-500 hover:text-primary'); ?>">
                <div class="relative">
                    <svg class="w-6 h-6 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M22 10V6c0-1.11-.9-2-2-2H4c-1.11 0-2 .89-2 2v4c1.11 0 2 .89 2 2s-.89 2-2 2v4c0 1.11.89 2 2 2h16c1.1 0 2-.89 2-2v-4c-1.1 0-2-.89-2-2s.9-2 2-2zm-9 7.5h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2v-2h2v2z"/>
                    </svg>
                    <?php if(request()->routeIs('tickets.*')): ?>
                        <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"></div>
                    <?php endif; ?>
                    <!-- Notification Badge -->
                    <?php if(auth()->user()->tickets()->where('status', 'active')->count() > 0): ?>
                        <div class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                            <?php echo e(auth()->user()->tickets()->where('status', 'active')->count()); ?>

                        </div>
                    <?php endif; ?>
                </div>
                <span class="text-xs mt-1 font-medium">Tiket</span>
            </a>
        <?php else: ?>
            <a href="<?php echo e(route('login')); ?>" class="flex flex-col items-center p-3 transition-all duration-300 text-gray-500 hover:text-primary">
                <div class="relative">
                    <svg class="w-6 h-6 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M22 10V6c0-1.11-.9-2-2-2H4c-1.11 0-2 .89-2 2v4c1.11 0 2 .89 2 2s-.89 2-2 2v4c0 1.11.89 2 2 2h16c1.1 0 2-.89 2-2v-4c-1.1 0-2-.89-2-2s.9-2 2-2zm-9 7.5h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2v-2h2v2z"/>
                    </svg>
                </div>
                <span class="text-xs mt-1 font-medium">Tiket</span>
            </a>
        <?php endif; ?>

        <!-- Profile -->
        <?php if(auth()->guard()->check()): ?>
            <a href="<?php echo e(route('profile')); ?>" class="flex flex-col items-center p-3 transition-all duration-300 <?php echo e(request()->routeIs('profile') ? 'text-primary scale-110' : 'text-gray-500 hover:text-primary'); ?>">
                <div class="relative">
                    <img src="<?php echo e(auth()->user()->avatar_url); ?>" alt="Profile" class="w-6 h-6 rounded-full border-2 <?php echo e(request()->routeIs('profile') ? 'border-primary' : 'border-gray-300'); ?> transition-all duration-300">
                    <?php if(request()->routeIs('profile')): ?>
                        <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"></div>
                    <?php endif; ?>
                </div>
                <span class="text-xs mt-1 font-medium">Profil</span>
            </a>
        <?php else: ?>
            <a href="<?php echo e(route('login')); ?>" class="flex flex-col items-center p-3 transition-all duration-300 text-gray-500 hover:text-primary">
                <div class="relative">
                    <svg class="w-6 h-6 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                </div>
                <span class="text-xs mt-1 font-medium">Masuk</span>
            </a>
        <?php endif; ?>
    </div>
</nav><?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/layouts/footer-floating.blade.php ENDPATH**/ ?>