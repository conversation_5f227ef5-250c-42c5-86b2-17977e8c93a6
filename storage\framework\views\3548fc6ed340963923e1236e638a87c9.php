<?php $__env->startSection('title', 'Wishlist Saya - TiXara'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-primary/5 via-white to-secondary/5">
    <!-- Header -->
    <section class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900" data-aos="fade-right">
                        <i data-lucide="heart" class="w-8 h-8 inline mr-3 text-red-500"></i>
                        Wishlist Saya
                    </h1>
                    <p class="text-gray-600 mt-2" data-aos="fade-right" data-aos-delay="100">
                        Event-event yang Anda simpan untuk nanti
                    </p>
                </div>
                
                <?php if($wishlistEvents->count() > 0): ?>
                    <div class="flex space-x-3" data-aos="fade-left">
                        <button onclick="clearWishlist()" 
                                class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200">
                            <i data-lucide="trash-2" class="w-4 h-4 inline mr-2"></i>
                            Hapus Semua
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Filters -->
    <?php if($wishlistEvents->count() > 0): ?>
        <section class="bg-white border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4" data-aos="fade-up">
                    <!-- Search -->
                    <div class="lg:col-span-2">
                        <div class="relative">
                            <input type="text" 
                                   name="search" 
                                   value="<?php echo e(request('search')); ?>"
                                   placeholder="Cari event di wishlist..."
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <i data-lucide="search" class="w-5 h-5 absolute left-3 top-2.5 text-gray-400"></i>
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div>
                        <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">Semua Kategori</option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                    <?php echo e($category->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Date Range Filter -->
                    <div>
                        <select name="date_range" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">Semua Tanggal</option>
                            <option value="this_week" <?php echo e(request('date_range') == 'this_week' ? 'selected' : ''); ?>>Minggu Ini</option>
                            <option value="this_month" <?php echo e(request('date_range') == 'this_month' ? 'selected' : ''); ?>>Bulan Ini</option>
                            <option value="next_month" <?php echo e(request('date_range') == 'next_month' ? 'selected' : ''); ?>>Bulan Depan</option>
                        </select>
                    </div>

                    <!-- Sort -->
                    <div>
                        <select name="sort" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="date_asc" <?php echo e(request('sort') == 'date_asc' ? 'selected' : ''); ?>>Tanggal (Terlama)</option>
                            <option value="date_desc" <?php echo e(request('sort') == 'date_desc' ? 'selected' : ''); ?>>Tanggal (Terbaru)</option>
                            <option value="price_asc" <?php echo e(request('sort') == 'price_asc' ? 'selected' : ''); ?>>Harga (Termurah)</option>
                            <option value="price_desc" <?php echo e(request('sort') == 'price_desc' ? 'selected' : ''); ?>>Harga (Termahal)</option>
                            <option value="name_asc" <?php echo e(request('sort') == 'name_asc' ? 'selected' : ''); ?>>Nama (A-Z)</option>
                            <option value="name_desc" <?php echo e(request('sort') == 'name_desc' ? 'selected' : ''); ?>>Nama (Z-A)</option>
                        </select>
                    </div>

                    <!-- Submit Button -->
                    <div class="lg:col-span-5 flex justify-end">
                        <button type="submit" 
                                class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200">
                            <i data-lucide="filter" class="w-4 h-4 inline mr-2"></i>
                            Filter
                        </button>
                    </div>
                </form>
            </div>
        </section>
    <?php endif; ?>

    <!-- Content -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <?php if($wishlistEvents->count() > 0): ?>
                <!-- Results Info -->
                <div class="flex items-center justify-between mb-8" data-aos="fade-up">
                    <p class="text-gray-600">
                        Menampilkan <?php echo e($wishlistEvents->firstItem()); ?>-<?php echo e($wishlistEvents->lastItem()); ?> 
                        dari <?php echo e($wishlistEvents->total()); ?> event
                    </p>
                </div>

                <!-- Events Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <?php $__currentLoopData = $wishlistEvents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="relative" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                            <?php echo $__env->make('components.event-card', ['event' => $event], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            
                            <!-- Remove from Wishlist Button -->
                            <button onclick="removeFromWishlist(<?php echo e($event->id); ?>)"
                                    class="absolute top-2 left-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors duration-200 shadow-lg"
                                    title="Hapus dari wishlist">
                                <i data-lucide="x" class="w-4 h-4"></i>
                            </button>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <?php if($wishlistEvents->hasPages()): ?>
                    <div class="mt-12" data-aos="fade-up">
                        <?php echo e($wishlistEvents->links()); ?>

                    </div>
                <?php endif; ?>
            <?php else: ?>
                <!-- Empty State -->
                <div class="text-center py-16" data-aos="fade-up">
                    <div class="w-32 h-32 mx-auto mb-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <i data-lucide="heart" class="w-16 h-16 text-gray-400"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Wishlist Kosong</h3>
                    <p class="text-gray-600 mb-8 max-w-md mx-auto">
                        Anda belum menambahkan event apapun ke wishlist. 
                        Mulai jelajahi event menarik dan simpan yang Anda sukai!
                    </p>
                    <a href="<?php echo e(route('tickets.index')); ?>" 
                       class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200">
                        <i data-lucide="search" class="w-5 h-5 mr-2"></i>
                        Jelajahi Event
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
async function removeFromWishlist(eventId) {
    if (!confirm('Hapus event ini dari wishlist?')) {
        return;
    }

    try {
        const response = await fetch(`/wishlist/remove/${eventId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (response.ok) {
            // Remove the event card with animation
            const eventCard = document.querySelector(`[data-event-id="${eventId}"]`).closest('.relative');
            eventCard.style.transform = 'scale(0)';
            eventCard.style.opacity = '0';
            
            setTimeout(() => {
                eventCard.remove();
                
                // Check if no more events
                const remainingCards = document.querySelectorAll('.grid .relative').length;
                if (remainingCards === 0) {
                    location.reload();
                }
            }, 300);

            window.showNotification(data.message, 'success');
        } else {
            window.showNotification(data.error || 'Terjadi kesalahan', 'error');
        }
    } catch (error) {
        console.error('Remove wishlist error:', error);
        window.showNotification('Terjadi kesalahan jaringan', 'error');
    }
}

async function clearWishlist() {
    if (!confirm('Hapus semua event dari wishlist? Tindakan ini tidak dapat dibatalkan.')) {
        return;
    }

    try {
        const response = await fetch('/wishlist/clear', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        if (response.ok) {
            window.showNotification('Semua wishlist berhasil dihapus', 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            window.showNotification('Terjadi kesalahan', 'error');
        }
    } catch (error) {
        console.error('Clear wishlist error:', error);
        window.showNotification('Terjadi kesalahan jaringan', 'error');
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/wishlist/index.blade.php ENDPATH**/ ?>