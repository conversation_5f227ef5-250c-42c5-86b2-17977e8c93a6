<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['categories']) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['categories']); ?>
<?php foreach (array_filter((['categories']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php if($categories->isNotEmpty()): ?>
<section class="py-16 bg-gradient-to-br from-gray-50 to-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center px-4 py-2 bg-red-100 text-red-800 rounded-full text-sm font-medium mb-4" data-aos="fade-up">
                <i data-lucide="trending-up" class="w-4 h-4 mr-2"></i>
                Trending Minggu Ini
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-4" data-aos="fade-up" data-aos-delay="100">
                🔥 Kategori Paling Populer
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                Kategori dengan aktivitas pembelian tiket tertinggi dalam 7 hari terakhir
            </p>
        </div>

        <!-- Trending Categories Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" 
                 data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                
                <!-- Background with Gradient -->
                <div class="aspect-square bg-gradient-to-br from-gray-200 to-gray-300 relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br opacity-90" 
                         style="background: linear-gradient(135deg, <?php echo e($category->color); ?>40, <?php echo e($category->color); ?>60)"></div>
                    
                    <!-- Trending Badge -->
                    <div class="absolute top-4 right-4 bg-red-500 text-white text-xs px-3 py-1 rounded-full font-bold shadow-lg z-10">
                        #<?php echo e($index + 1); ?>

                    </div>
                    
                    <!-- Fire Animation -->
                    <div class="absolute top-4 left-4 text-orange-500 animate-pulse z-10">
                        <i data-lucide="flame" class="w-6 h-6"></i>
                    </div>
                    
                    <!-- Decorative Elements -->
                    <div class="absolute inset-0 opacity-20">
                        <div class="absolute top-8 right-8 w-16 h-16 rounded-full bg-white/30 backdrop-blur-sm"></div>
                        <div class="absolute bottom-8 left-8 w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm"></div>
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 rounded-full bg-white/10 backdrop-blur-sm"></div>
                    </div>
                    
                    <!-- Content -->
                    <div class="absolute inset-0 flex flex-col items-center justify-center text-center p-6 z-10">
                        <!-- Icon -->
                        <div class="w-20 h-20 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-xl">
                            <i data-lucide="<?php echo e($category->icon); ?>" class="w-10 h-10" style="color: <?php echo e($category->color); ?>"></i>
                        </div>
                        
                        <!-- Category Info -->
                        <h3 class="font-bold text-white text-lg mb-2 group-hover:text-yellow-200 transition-colors drop-shadow-lg">
                            <?php echo e($category->name); ?>

                        </h3>
                        <p class="text-white/90 text-sm mb-4 line-clamp-2 drop-shadow">
                            <?php echo e(Str::limit($category->description, 60)); ?>

                        </p>
                        
                        <!-- Stats -->
                        <div class="text-white/80 text-sm mb-4">
                            <span class="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full">
                                Hot! 🔥
                            </span>
                        </div>
                    </div>
                    
                    <!-- Hover Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-6 z-20">
                        <a href="<?php echo e(route('categories.show', $category->slug)); ?>" 
                           class="w-full bg-white text-gray-900 py-3 px-4 rounded-xl text-center font-bold hover:bg-yellow-100 transition-colors shadow-lg">
                            Lihat Event Trending
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-12" data-aos="fade-up" data-aos-delay="400">
            <div class="inline-flex items-center space-x-4">
                <a href="<?php echo e(route('categories.index')); ?>" 
                   class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-full font-semibold hover:bg-primary/90 transition-colors shadow-lg hover:shadow-xl">
                    <i data-lucide="grid-3x3" class="w-5 h-5 mr-2"></i>
                    Jelajahi Semua Kategori
                </a>
                <a href="<?php echo e(route('tickets.index')); ?>" 
                   class="inline-flex items-center px-6 py-3 bg-white border-2 border-primary text-primary rounded-full font-semibold hover:bg-primary hover:text-white transition-all duration-300 shadow-lg hover:shadow-xl">
                    <i data-lucide="ticket" class="w-5 h-5 mr-2"></i>
                    Lihat Semua Event
                </a>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>
<?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/components/trending-categories.blade.php ENDPATH**/ ?>