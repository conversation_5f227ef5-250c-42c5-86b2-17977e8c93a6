<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Event;
use App\Models\Notification;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Health check endpoint
Route::get('/health-check', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'service' => 'TikPro API',
        'version' => '1.0.0'
    ]);
});

// Search API
Route::get('/search', function (Request $request) {
    $query = $request->get('q', '');

    if (strlen($query) < 2) {
        return response()->json(['results' => []]);
    }

    try {
        // Search events by title and description
        $events = Event::where('status', 'published')
            ->where(function ($q) use ($query) {
                $q->where('title', 'LIKE', "%{$query}%")
                  ->orWhere('description', 'LIKE', "%{$query}%")
                  ->orWhere('location', 'LIKE', "%{$query}%");
            })
            ->limit(10)
            ->get(['id', 'title', 'description', 'location', 'slug']);

        $results = $events->map(function ($event) {
            return [
                'id' => $event->id,
                'title' => $event->title,
                'description' => \Str::limit($event->description, 100),
                'url' => route('tickets.show', $event->slug)
            ];
        });

        return response()->json(['results' => $results]);
    } catch (\Exception $e) {
        return response()->json(['results' => [], 'error' => 'Search failed'], 500);
    }
});

// Notifications API (requires authentication)
Route::middleware('auth:sanctum')->group(function () {
    // Get latest notifications
    Route::get('/notifications/latest', function (Request $request) {
        try {
            $user = $request->user();
            $lastCheck = $request->get('last_check');

            $query = $user->notifications()->latest();

            if ($lastCheck) {
                $query->where('created_at', '>', $lastCheck);
            }

            $notifications = $query->limit(20)->get();
            $unreadCount = $user->unreadNotifications()->count();

            return response()->json([
                'notifications' => $notifications->map(function ($notification) {
                    return [
                        'id' => $notification->id,
                        'title' => $notification->data['title'] ?? 'Notifikasi',
                        'message' => $notification->data['message'] ?? '',
                        'type' => $notification->data['type'] ?? 'info',
                        'created_at' => $notification->created_at->toISOString(),
                        'read_at' => $notification->read_at?->toISOString(),
                        'action_url' => $notification->data['action_url'] ?? null
                    ];
                }),
                'unread_count' => $unreadCount,
                'has_new' => $lastCheck ? $notifications->count() > 0 : false
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to load notifications'], 500);
        }
    });

    // Mark notification as read
    Route::post('/notifications/{id}/read', function (Request $request, $id) {
        try {
            $user = $request->user();
            $notification = $user->notifications()->find($id);

            if ($notification) {
                $notification->markAsRead();
                return response()->json(['success' => true]);
            }

            return response()->json(['error' => 'Notification not found'], 404);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to mark notification as read'], 500);
        }
    });

    // Mark all notifications as read
    Route::post('/notifications/mark-all-read', function (Request $request) {
        try {
            $user = $request->user();
            $user->unreadNotifications()->update(['read_at' => now()]);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to mark all notifications as read'], 500);
        }
    });
});
