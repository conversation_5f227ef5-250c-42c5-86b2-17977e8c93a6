<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;
use Intervention\Image\ImageManager;

/**
 * @method static \Intervention\Image\Interfaces\ImageInterface make(mixed $input)
 * @method static \Intervention\Image\Interfaces\ImageInterface read(mixed $input)
 * @method static \Intervention\Image\Interfaces\ImageInterface create(int $width, int $height)
 * @method static \Intervention\Image\ImageManager driver(\Intervention\Image\Interfaces\DriverInterface $driver)
 * @method static \Intervention\Image\ImageManager config(array $config)
 * 
 * @see \Intervention\Image\ImageManager
 */
class Image extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'image';
    }
}
