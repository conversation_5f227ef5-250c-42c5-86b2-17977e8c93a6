<?php $__env->startSection('title', $title); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <!-- Category Hero -->
    <div class="relative bg-gradient-to-r from-primary to-secondary text-white py-20 overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"white\" opacity=\"0.3\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <!-- Breadcrumb -->
                    <nav class="mb-6" data-aos="fade-up">
                        <ol class="flex items-center space-x-2 text-white/80">
                            <li><a href="<?php echo e(route('home')); ?>" class="hover:text-white transition-colors">Home</a></li>
                            <li><span class="mx-2">/</span></li>
                            <li><a href="<?php echo e(route('categories.index')); ?>" class="hover:text-white transition-colors">Kategori</a></li>
                            <li><span class="mx-2">/</span></li>
                            <li class="text-white font-medium"><?php echo e($category->name); ?></li>
                        </ol>
                    </nav>
                    
                    <!-- Category Info -->
                    <div class="flex items-center gap-6 mb-6" data-aos="fade-up" data-aos-delay="100">
                        <div class="w-20 h-20 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
                            <i data-lucide="<?php echo e($category->icon); ?>" class="w-10 h-10 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-4xl md:text-5xl font-bold mb-2"><?php echo e($category->name); ?></h1>
                            <p class="text-xl text-white/90 max-w-2xl"><?php echo e($category->description); ?></p>
                        </div>
                    </div>
                    
                    <!-- Stats -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-6" data-aos="fade-up" data-aos-delay="200">
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                            <div class="text-2xl font-bold"><?php echo e($stats['total_events']); ?></div>
                            <div class="text-sm text-white/80">Total Event</div>
                        </div>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                            <div class="text-2xl font-bold"><?php echo e($stats['upcoming_events']); ?></div>
                            <div class="text-sm text-white/80">Event Mendatang</div>
                        </div>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                            <div class="text-2xl font-bold">Rp <?php echo e(number_format($stats['avg_price'], 0, ',', '.')); ?></div>
                            <div class="text-sm text-white/80">Rata-rata Harga</div>
                        </div>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                            <div class="text-2xl font-bold">Rp <?php echo e(number_format($stats['total_revenue'], 0, ',', '.')); ?></div>
                            <div class="text-sm text-white/80">Total Revenue</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters & Search -->
    <div class="bg-white shadow-sm border-b sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <!-- Search -->
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <input type="text" 
                               id="eventSearch"
                               placeholder="Cari event dalam kategori ini..." 
                               class="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                            <i data-lucide="search" class="w-4 h-4 text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="flex flex-wrap items-center gap-3">
                    <!-- Location Filter -->
                    <select name="location" class="filter-select px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                        <option value="">Semua Lokasi</option>
                        <option value="Jakarta">Jakarta</option>
                        <option value="Bandung">Bandung</option>
                        <option value="Surabaya">Surabaya</option>
                        <option value="Yogyakarta">Yogyakarta</option>
                        <option value="Medan">Medan</option>
                    </select>
                    
                    <!-- Date Filter -->
                    <select name="date_range" class="filter-select px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                        <option value="">Semua Tanggal</option>
                        <option value="today">Hari Ini</option>
                        <option value="tomorrow">Besok</option>
                        <option value="this_week">Minggu Ini</option>
                        <option value="this_month">Bulan Ini</option>
                    </select>
                    
                    <!-- Price Filter -->
                    <select name="price_range" class="filter-select px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                        <option value="">Semua Harga</option>
                        <option value="free">Gratis</option>
                        <option value="under_100k">< Rp 100.000</option>
                        <option value="100k_500k">Rp 100.000 - 500.000</option>
                        <option value="above_500k">> Rp 500.000</option>
                    </select>
                    
                    <!-- Sort -->
                    <select name="sort" class="filter-select px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                        <option value="date">Tanggal Terdekat</option>
                        <option value="price_low">Harga Terendah</option>
                        <option value="price_high">Harga Tertinggi</option>
                        <option value="popular">Paling Populer</option>
                    </select>
                    
                    <!-- Clear Filters -->
                    <button id="clearFilters" class="px-4 py-2 text-gray-600 hover:text-gray-800 text-sm font-medium">
                        Reset Filter
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Events Grid -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Loading State -->
        <div id="loadingState" class="hidden text-center py-16">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p class="mt-4 text-gray-600">Memuat event...</p>
        </div>
        
        <!-- Events Grid -->
        <div id="eventsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <?php $__empty_1 = true; $__currentLoopData = $events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="event-card bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group transform hover:-translate-y-1" 
                 data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 50); ?>">
                
                <!-- Event Image -->
                <div class="relative h-48 bg-gradient-to-br from-gray-200 to-gray-300 overflow-hidden">
                    <?php if($event->poster): ?>
                        <img src="<?php echo e($event->poster); ?>" alt="<?php echo e($event->title); ?>" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                    <?php else: ?>
                        <div class="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                            <i data-lucide="<?php echo e($category->icon); ?>" class="w-16 h-16 text-primary/60"></i>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Price Badge -->
                    <div class="absolute top-3 right-3 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-semibold text-gray-900">
                        <?php if($event->price == 0): ?>
                            Gratis
                        <?php else: ?>
                            Rp <?php echo e(number_format($event->price, 0, ',', '.')); ?>

                        <?php endif; ?>
                    </div>
                    
                    <!-- Date Badge -->
                    <div class="absolute bottom-3 left-3 bg-primary text-white px-3 py-1 rounded-lg text-sm font-medium">
                        <?php echo e($event->start_date->format('d M')); ?>

                    </div>
                </div>
                
                <!-- Event Content -->
                <div class="p-6">
                    <h3 class="font-bold text-lg text-gray-900 mb-2 line-clamp-2 group-hover:text-primary transition-colors">
                        <?php echo e($event->title); ?>

                    </h3>
                    
                    <div class="space-y-2 mb-4 text-sm text-gray-600">
                        <div class="flex items-center gap-2">
                            <i data-lucide="calendar" class="w-4 h-4"></i>
                            <span><?php echo e($event->start_date->format('d M Y, H:i')); ?></span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i data-lucide="map-pin" class="w-4 h-4"></i>
                            <span class="line-clamp-1"><?php echo e($event->venue_name); ?>, <?php echo e($event->city); ?></span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i data-lucide="user" class="w-4 h-4"></i>
                            <span><?php echo e($event->organizer->name); ?></span>
                        </div>
                    </div>
                    
                    <!-- Capacity Info -->
                    <div class="mb-4">
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Sisa Tiket</span>
                            <span><?php echo e($event->available_capacity); ?>/<?php echo e($event->total_capacity); ?></span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-primary h-2 rounded-full" style="width: <?php echo e(($event->available_capacity / $event->total_capacity) * 100); ?>%"></div>
                        </div>
                    </div>
                    
                    <!-- Action Button -->
                    <a href="<?php echo e(route('tickets.show', $event->slug)); ?>" 
                       class="block w-full bg-gradient-to-r from-primary to-secondary text-white text-center py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 group-hover:from-secondary group-hover:to-primary">
                        Lihat Detail
                    </a>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-full text-center py-16">
                <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                    <i data-lucide="calendar-x" class="w-12 h-12 text-gray-400"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum ada event</h3>
                <p class="text-gray-600">Event untuk kategori ini akan segera hadir. Pantau terus ya!</p>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Pagination -->
        <?php if($events->hasPages()): ?>
        <div class="mt-12 flex justify-center">
            <?php echo e($events->links()); ?>

        </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('.filter-select');
    const searchInput = document.getElementById('eventSearch');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const eventsGrid = document.getElementById('eventsGrid');
    const loadingState = document.getElementById('loadingState');
    
    let debounceTimer;
    
    // Filter change handler
    function handleFilterChange() {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
            applyFilters();
        }, 300);
    }
    
    // Apply filters
    function applyFilters() {
        const filters = {};
        
        // Get filter values
        filterSelects.forEach(select => {
            if (select.value) {
                filters[select.name] = select.value;
            }
        });
        
        // Add search query
        if (searchInput.value.trim()) {
            filters.search = searchInput.value.trim();
        }
        
        // Show loading
        showLoading();
        
        // Build URL with filters
        const url = new URL(window.location.href);
        Object.keys(filters).forEach(key => {
            url.searchParams.set(key, filters[key]);
        });
        
        // Remove empty filters
        filterSelects.forEach(select => {
            if (!select.value) {
                url.searchParams.delete(select.name);
            }
        });
        
        if (!searchInput.value.trim()) {
            url.searchParams.delete('search');
        }
        
        // Fetch filtered results
        fetch(url.href, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            updateEventsGrid(data.events);
            hideLoading();
            
            // Update URL without page reload
            window.history.pushState({}, '', url.href);
        })
        .catch(error => {
            console.error('Error:', error);
            hideLoading();
        });
    }
    
    // Update events grid
    function updateEventsGrid(events) {
        // This would need to be implemented based on your pagination structure
        // For now, we'll just reload the page
        window.location.href = window.location.href;
    }
    
    // Show loading state
    function showLoading() {
        eventsGrid.style.opacity = '0.5';
        loadingState.classList.remove('hidden');
    }
    
    // Hide loading state
    function hideLoading() {
        eventsGrid.style.opacity = '1';
        loadingState.classList.add('hidden');
    }
    
    // Clear all filters
    clearFiltersBtn.addEventListener('click', function() {
        filterSelects.forEach(select => select.value = '');
        searchInput.value = '';
        
        // Remove all query parameters
        const url = new URL(window.location.href);
        url.search = '';
        window.location.href = url.href;
    });
    
    // Add event listeners
    filterSelects.forEach(select => {
        select.addEventListener('change', handleFilterChange);
    });
    
    searchInput.addEventListener('input', handleFilterChange);
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/pages/categories/show.blade.php ENDPATH**/ ?>