<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Event;
use App\Models\User;
use App\Models\Order;
use App\Models\Ticket;
use Carbon\Carbon;
use Illuminate\Support\Str;

class TicketSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get published tickets and buyers
        $tickets = Event::where('status', 'published')->get();
        $buyers = User::where('role', 'pembeli')->get();

        if ($tickets->isEmpty() || $buyers->isEmpty()) {
            $this->command->warn('Please run Ticketseeder and UserSeeder first');
            return;
        }

        // Create sample orders and tickets
        foreach ($tickets->take(5) as $event) {
            // Create 3-5 orders per event
            $orderCount = rand(3, 5);

            for ($i = 0; $i < $orderCount; $i++) {
                $buyer = $buyers->random();
                $quantity = rand(1, 3);
                $unitPrice = $event->price;
                $subtotal = $unitPrice * $quantity;
                $adminFee = max(2500, min(50000, $subtotal * 0.05));
                $totalAmount = $subtotal + $adminFee;

                // Create order
                $order = Order::create([
                    'order_number' => $this->generateOrderNumber(),
                    'user_id' => $buyer->id,
                    'tiket_id' => $event->id,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'subtotal' => $subtotal,
                    'admin_fee' => $adminFee,
                    'discount_amount' => 0,
                    'total_amount' => $totalAmount,
                    'payment_method' => collect(['bank_transfer', 'e_wallet', 'credit_card'])->random(),
                    'customer_name' => $buyer->name,
                    'customer_email' => $buyer->email,
                    'customer_phone' => $buyer->phone ?? '08' . rand(**********, **********),
                    'expires_at' => now()->addMinutes(30),
                    'status' => 'confirmed',
                    'payment_status' => 'paid',
                    'payment_reference' => 'PAY-' . strtoupper(uniqid()),
                    'paid_at' => now()->subMinutes(rand(10, 1440)), // Paid 10 minutes to 1 day ago
                    'confirmed_at' => now()->subMinutes(rand(5, 1440)),
                ]);

                // Create tickets for this order
                for ($j = 0; $j < $quantity; $j++) {
                    $ticketStatus = $this->getRandomTicketStatus($event);

                    $ticket = Ticket::create([
                        'ticket_number' => $this->generateTicketNumber(),
                        'qr_code' => $this->generateQRCode(),
                        'tiket_id' => $event->id,
                        'buyer_id' => $buyer->id,
                        'order_id' => $order->id,
                        'attendee_name' => $buyer->name,
                        'attendee_email' => $buyer->email,
                        'attendee_phone' => $buyer->phone ?? '08' . rand(**********, **********),
                        'price' => $unitPrice,
                        'admin_fee' => $adminFee / $quantity,
                        'total_paid' => $totalAmount / $quantity,
                        'status' => $ticketStatus,
                        'download_token' => Str::random(32),
                        'download_count' => $ticketStatus === 'active' ? rand(0, 3) : 0,
                        'last_downloaded_at' => $ticketStatus === 'active' ? now()->subHours(rand(1, 48)) : null,
                        'used_at' => $ticketStatus === 'used' ? $event->start_date->addHours(rand(0, 4)) : null,
                        'validated_by' => $ticketStatus === 'used' ? User::where('role', 'staff')->inRandomOrder()->first()?->id : null,
                        'cancelled_at' => $ticketStatus === 'cancelled' ? now()->subDays(rand(1, 7)) : null,
                        'cancellation_reason' => $ticketStatus === 'cancelled' ? 'Tidak bisa hadir' : null,
                    ]);

                    // Generate QR code path
                    $ticket->update([
                        'qr_code_path' => "qr-codes/{$ticket->ticket_number}.png"
                    ]);
                }

                // Update event capacity
                $event->decrement('available_capacity', $quantity);
            }
        }

        // Create some tickets for current user if exists
        $currentUser = User::where('email', '<EMAIL>')->first();
        if ($currentUser && $currentUser->role === 'pembeli') {
            $this->createTicketsForUser($currentUser, $tickets->take(3));
        }

        $this->command->info('Tickets seeded successfully!');
    }

    /**
     * Create tickets for specific user
     */
    private function createTicketsForUser(User $user, $tickets): void
    {
        foreach ($tickets as $event) {
            $quantity = rand(1, 2);
            $unitPrice = $event->price;
            $subtotal = $unitPrice * $quantity;
            $adminFee = max(2500, min(50000, $subtotal * 0.05));
            $totalAmount = $subtotal + $adminFee;

            // Create order
            $order = Order::create([
                'order_number' => $this->generateOrderNumber(),
                'user_id' => $user->id,
                'event_id' => $event->id,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'subtotal' => $subtotal,
                'admin_fee' => $adminFee,
                'discount_amount' => 0,
                'total_amount' => $totalAmount,
                'payment_method' => 'e_wallet',
                'customer_name' => $user->name,
                'customer_email' => $user->email,
                'customer_phone' => $user->phone ?? '081234567890',
                'expires_at' => now()->addMinutes(30),
                'status' => 'confirmed',
                'payment_status' => 'paid',
                'payment_reference' => 'PAY-' . strtoupper(uniqid()),
                'paid_at' => now()->subHours(rand(1, 24)),
                'confirmed_at' => now()->subHours(rand(1, 24)),
            ]);

            // Create tickets
            for ($i = 0; $i < $quantity; $i++) {
                $ticket = Ticket::create([
                    'ticket_number' => $this->generateTicketNumber(),
                    'qr_code' => $this->generateQRCode(),
                    'event_id' => $event->id,
                    'buyer_id' => $user->id,
                    'order_id' => $order->id,
                    'attendee_name' => $user->name,
                    'attendee_email' => $user->email,
                    'attendee_phone' => $user->phone ?? '081234567890',
                    'price' => $unitPrice,
                    'admin_fee' => $adminFee / $quantity,
                    'total_paid' => $totalAmount / $quantity,
                    'status' => 'active',
                    'download_token' => Str::random(32),
                    'download_count' => rand(0, 2),
                    'last_downloaded_at' => now()->subHours(rand(1, 12)),
                ]);

                $ticket->update([
                    'qr_code_path' => "qr-codes/{$ticket->ticket_number}.png"
                ]);
            }
        }
    }

    /**
     * Get random ticket status based on event date
     */
    private function getRandomTicketStatus(Event $event): string
    {
        // If event is in the past, ticket might be used
        if ($event->start_date < now()) {
            return collect(['used', 'active'])->random();
        }

        // If event is far in future, ticket is likely active
        if ($event->start_date > now()->addDays(7)) {
            $statuses = ['active', 'active', 'active', 'cancelled'];
            return collect($statuses)->random();
        }

        // If event is soon, ticket is mostly active
        return collect(['active', 'active', 'active', 'active', 'cancelled'])->random();
    }

    /**
     * Generate unique order number
     */
    private function generateOrderNumber(): string
    {
        do {
            $orderNumber = 'ORD-' . date('Ymd') . '-' . strtoupper(Str::random(5));
        } while (Order::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Generate unique ticket number
     */
    private function generateTicketNumber(): string
    {
        do {
            $ticketNumber = 'TIK-' . date('Ymd') . '-' . strtoupper(Str::random(6));
        } while (Ticket::where('ticket_number', $ticketNumber)->exists());

        return $ticketNumber;
    }

    /**
     * Generate unique QR code
     */
    private function generateQRCode(): string
    {
        do {
            $qrCode = 'QR-' . Str::random(20);
        } while (Ticket::where('qr_code', $qrCode)->exists());

        return $qrCode;
    }
}
