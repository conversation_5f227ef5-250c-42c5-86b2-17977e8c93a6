@extends('layouts.main')

@section('title', 'Login Credentials - TikPro')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10 py-12">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">🔑 Login Credentials</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Gunakan kredensial di bawah ini untuk login ke sistem TikPro. 
                Semua akun sudah aktif dan terverifikasi.
            </p>
        </div>

        <!-- Quick Login Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
            <button onclick="quickLogin('<EMAIL>', 'TikPro@2024')" 
                    class="p-4 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors shadow-lg">
                <div class="text-center">
                    <div class="text-2xl mb-2">👑</div>
                    <div class="font-bold">Quick Login Admin</div>
                    <div class="text-sm opacity-90"><EMAIL></div>
                </div>
            </button>
            
            <button onclick="quickLogin('<EMAIL>', 'Staff@2024')" 
                    class="p-4 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors shadow-lg">
                <div class="text-center">
                    <div class="text-2xl mb-2">👥</div>
                    <div class="font-bold">Quick Login Staff</div>
                    <div class="text-sm opacity-90"><EMAIL></div>
                </div>
            </button>
            
            <button onclick="quickLogin('<EMAIL>', 'Penjual@2024')" 
                    class="p-4 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors shadow-lg">
                <div class="text-center">
                    <div class="text-2xl mb-2">🎪</div>
                    <div class="font-bold">Quick Login Organizer</div>
                    <div class="text-sm opacity-90"><EMAIL></div>
                </div>
            </button>
            
            <button onclick="quickLogin('<EMAIL>', 'Pembeli@2024')" 
                    class="p-4 bg-purple-500 text-white rounded-xl hover:bg-purple-600 transition-colors shadow-lg">
                <div class="text-center">
                    <div class="text-2xl mb-2">🛒</div>
                    <div class="font-bold">Quick Login Customer</div>
                    <div class="text-sm opacity-90"><EMAIL></div>
                </div>
            </button>
        </div>

        <!-- Credentials Tables -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Admin Accounts -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                <div class="bg-red-500 text-white p-6">
                    <h2 class="text-2xl font-bold flex items-center">
                        <span class="mr-3">👑</span>
                        Admin Accounts
                    </h2>
                    <p class="text-red-100 mt-2">Full system access and management</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-semibold text-gray-900">Admin TikPro</div>
                                    <div class="text-sm text-gray-600"><EMAIL></div>
                                    <div class="text-sm font-mono bg-gray-200 px-2 py-1 rounded mt-1 inline-block">TikPro@2024</div>
                                </div>
                                <button onclick="copyCredentials('<EMAIL>', 'TikPro@2024')" 
                                        class="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 transition-colors text-sm">
                                    Copy
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Staff Accounts -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                <div class="bg-blue-500 text-white p-6">
                    <h2 class="text-2xl font-bold flex items-center">
                        <span class="mr-3">👥</span>
                        Staff Accounts
                    </h2>
                    <p class="text-blue-100 mt-2">Limited administrative access</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-semibold text-gray-900">Staff TikPro</div>
                                    <div class="text-sm text-gray-600"><EMAIL></div>
                                    <div class="text-sm font-mono bg-gray-200 px-2 py-1 rounded mt-1 inline-block">Staff@2024</div>
                                </div>
                                <button onclick="copyCredentials('<EMAIL>', 'Staff@2024')" 
                                        class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-sm">
                                    Copy
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Organizer Accounts -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                <div class="bg-green-500 text-white p-6">
                    <h2 class="text-2xl font-bold flex items-center">
                        <span class="mr-3">🎪</span>
                        Event Organizer Accounts
                    </h2>
                    <p class="text-green-100 mt-2">Create and manage events</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-semibold text-gray-900">Event Organizer 1</div>
                                    <div class="text-sm text-gray-600"><EMAIL></div>
                                    <div class="text-sm font-mono bg-gray-200 px-2 py-1 rounded mt-1 inline-block">Penjual@2024</div>
                                </div>
                                <button onclick="copyCredentials('<EMAIL>', 'Penjual@2024')" 
                                        class="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 transition-colors text-sm">
                                    Copy
                                </button>
                            </div>
                        </div>
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-semibold text-gray-900">Event Organizer 2</div>
                                    <div class="text-sm text-gray-600"><EMAIL></div>
                                    <div class="text-sm font-mono bg-gray-200 px-2 py-1 rounded mt-1 inline-block">Penjual@2024</div>
                                </div>
                                <button onclick="copyCredentials('<EMAIL>', 'Penjual@2024')" 
                                        class="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 transition-colors text-sm">
                                    Copy
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Accounts -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                <div class="bg-purple-500 text-white p-6">
                    <h2 class="text-2xl font-bold flex items-center">
                        <span class="mr-3">🛒</span>
                        Customer Accounts
                    </h2>
                    <p class="text-purple-100 mt-2">Browse and purchase tickets</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-semibold text-gray-900">John Doe</div>
                                    <div class="text-sm text-gray-600"><EMAIL></div>
                                    <div class="text-sm font-mono bg-gray-200 px-2 py-1 rounded mt-1 inline-block">Pembeli@2024</div>
                                </div>
                                <button onclick="copyCredentials('<EMAIL>', 'Pembeli@2024')" 
                                        class="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors text-sm">
                                    Copy
                                </button>
                            </div>
                        </div>
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-semibold text-gray-900">Jane Smith</div>
                                    <div class="text-sm text-gray-600"><EMAIL></div>
                                    <div class="text-sm font-mono bg-gray-200 px-2 py-1 rounded mt-1 inline-block">Pembeli@2024</div>
                                </div>
                                <button onclick="copyCredentials('<EMAIL>', 'Pembeli@2024')" 
                                        class="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors text-sm">
                                    Copy
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="mt-12 bg-yellow-50 border border-yellow-200 rounded-xl p-6">
            <div class="flex items-start">
                <div class="text-yellow-600 mr-3">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-yellow-800 mb-2">⚠️ Security Notice</h3>
                    <ul class="text-yellow-700 space-y-1 text-sm">
                        <li>• These credentials are for development and testing purposes only</li>
                        <li>• Change all passwords before deploying to production</li>
                        <li>• All accounts are pre-verified and active</li>
                        <li>• Use different credentials for each environment</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="mt-8 text-center space-x-4">
            <a href="{{ route('login') }}" 
               class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-xl hover:bg-primary/90 transition-colors shadow-lg">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                </svg>
                Go to Login Page
            </a>
            <a href="{{ route('home') }}" 
               class="inline-flex items-center px-6 py-3 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors shadow-lg">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                </svg>
                Back to Home
            </a>
        </div>
    </div>
</div>

<!-- Toast Notification -->
<div id="toast" class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 z-50">
    <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
        </svg>
        <span id="toastMessage">Credentials copied to clipboard!</span>
    </div>
</div>

<script>
function quickLogin(email, password) {
    // Create a form and submit it
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("login") }}';
    
    // CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '_token';
    csrfInput.value = '{{ csrf_token() }}';
    form.appendChild(csrfInput);
    
    // Email input
    const emailInput = document.createElement('input');
    emailInput.type = 'hidden';
    emailInput.name = 'email';
    emailInput.value = email;
    form.appendChild(emailInput);
    
    // Password input
    const passwordInput = document.createElement('input');
    passwordInput.type = 'hidden';
    passwordInput.name = 'password';
    passwordInput.value = password;
    form.appendChild(passwordInput);
    
    document.body.appendChild(form);
    form.submit();
}

function copyCredentials(email, password) {
    const text = `Email: ${email}\nPassword: ${password}`;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showToast('Credentials copied to clipboard!');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('Credentials copied to clipboard!');
    }
}

function showToast(message) {
    const toast = document.getElementById('toast');
    const toastMessage = document.getElementById('toastMessage');
    
    toastMessage.textContent = message;
    toast.classList.remove('translate-x-full');
    
    setTimeout(() => {
        toast.classList.add('translate-x-full');
    }, 3000);
}
</script>
@endsection
