@props(['event', 'delay' => 0, 'size' => 'default'])

<div class="card-hover bg-white rounded-2xl overflow-hidden shadow-lg {{ $size === 'large' ? 'md:col-span-2' : '' }}"
     data-aos="fade-up"
     data-aos-delay="{{ $delay }}">

    <div class="relative">
        <img src="{{ $event->poster_url }}"
             alt="{{ $event->title }}"
             class="w-full {{ $size === 'large' ? 'h-64' : 'h-48' }} object-cover">

        <!-- Event Status Badge -->
        @if($event->availability_status == 'limited')
            <div class="absolute top-4 left-4">
                <span class="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Terbatas
                </span>
            </div>
        @elseif($event->availability_status == 'sold_out')
            <div class="absolute top-4 left-4">
                <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Sold Out
                </span>
            </div>
        @elseif($event->is_featured)
            <div class="absolute top-4 left-4">
                <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Featured
                </span>
            </div>
        @elseif($event->is_free)
            <div class="absolute top-4 left-4">
                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Gratis
                </span>
            </div>
        @endif

        <!-- Discount Badge -->
        @if($event->discount_percentage)
            <div class="absolute top-4 right-16">
                <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                    -{{ $event->discount_percentage }}%
                </span>
            </div>
        @endif

        <!-- Wishlist Button -->
        <div class="absolute top-4 right-4">
            <button onclick="toggleWishlist({{ $event->id }})"
                    class="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors duration-200 wishlist-btn group"
                    data-event-id="{{ $event->id }}"
                    title="Tambah ke wishlist">
                <svg class="w-5 h-5 text-gray-600 group-hover:text-red-500 transition-colors duration-200"
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                </svg>
            </button>
        </div>

        <!-- Quick View Button (on hover) -->
        <div class="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <button onclick="quickView({{ $event->id }})"
                    class="bg-white text-gray-900 px-4 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200 transform translate-y-4 hover:translate-y-0">
                Quick View
            </button>
        </div>
    </div>

    <div class="p-6">
        <!-- Date & Category -->
        <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
            <div class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                <span>{{ $event->start_date->format('d M Y') }}</span>
                @if($event->start_date->isToday())
                    <span class="ml-2 bg-red-100 text-red-600 px-2 py-0.5 rounded-full text-xs font-semibold">
                        Hari Ini
                    </span>
                @elseif($event->start_date->isTomorrow())
                    <span class="ml-2 bg-blue-100 text-blue-600 px-2 py-0.5 rounded-full text-xs font-semibold">
                        Besok
                    </span>
                @elseif($event->start_date->diffInDays() <= 7)
                    <span class="ml-2 bg-yellow-100 text-yellow-600 px-2 py-0.5 rounded-full text-xs font-semibold">
                        {{ $event->start_date->diffInDays() }} hari lagi
                    </span>
                @endif
            </div>
            <span class="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs font-semibold">
                {{ $event->category->name }}
            </span>
        </div>

        <!-- Title -->
        <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2 hover:text-primary transition-colors duration-200">
            <a href="{{ route('tickets.show', $event) }}">{{ $event->title }}</a>
        </h3>

        <!-- Location -->
        <div class="flex items-center text-sm text-gray-500 mb-4">
            <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            <span class="truncate">{{ $event->venue_name }}, {{ $event->city }}</span>
        </div>

        <!-- Additional Info for Large Cards -->
        @if($size === 'large')
            <div class="mb-4">
                <p class="text-gray-600 text-sm line-clamp-2">
                    {{ Str::limit(strip_tags($event->description), 120) }}
                </p>
            </div>

            <!-- Organizer -->
            <div class="flex items-center text-sm text-gray-500 mb-4">
                <img src="{{ $event->organizer->avatar_url }}"
                     alt="{{ $event->organizer->name }}"
                     class="w-5 h-5 rounded-full mr-2">
                <span>{{ $event->organizer->name }}</span>
            </div>
        @endif

        <!-- Availability Indicator -->
        @if($event->availability_status == 'limited')
            <div class="mb-4">
                <div class="flex justify-between items-center text-xs text-gray-600 mb-1">
                    <span>Tiket tersisa</span>
                    <span>{{ $event->available_capacity }} / {{ $event->total_capacity }}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-1.5">
                    <div class="bg-orange-500 h-1.5 rounded-full transition-all duration-300"
                         style="width: {{ ($event->available_capacity / $event->total_capacity) * 100 }}%"></div>
                </div>
            </div>
        @endif

        <!-- Price & Action -->
        <div class="flex justify-between items-center">
            <div>
                <span class="text-xl font-bold text-primary">{{ $event->formatted_price }}</span>
                @if($event->discount_percentage)
                    <div class="text-sm text-gray-500 line-through">
                        Rp {{ number_format($event->original_price, 0, ',', '.') }}
                    </div>
                @endif
            </div>

            @if($event->canPurchaseTickets())
                <a href="{{ route('tickets.show', $event) }}"
                   class="bg-gradient-to-r from-primary to-secondary text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 font-semibold text-sm transform hover:scale-105">
                    {{ $size === 'large' ? 'Lihat Detail' : 'Detail' }}
                </a>
            @else
                @if($event->availability_status == 'sold_out')
                    <span class="bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-semibold cursor-not-allowed">
                        Sold Out
                    </span>
                @elseif($event->hasStarted())
                    <span class="bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-semibold cursor-not-allowed">
                        Dimulai
                    </span>
                @else
                    <a href="{{ route('tickets.show', $event) }}"
                       class="bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-semibold cursor-not-allowed">
                        Detail
                    </a>
                @endif
            @endif
        </div>

        <!-- Tags (for large cards) -->
        @if($size === 'large' && $event->tags && count($event->tags) > 0)
            <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex flex-wrap gap-1">
                    @foreach(array_slice($event->tags, 0, 3) as $tag)
                        <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">
                            #{{ $tag }}
                        </span>
                    @endforeach
                    @if(count($event->tags) > 3)
                        <span class="text-gray-500 text-xs px-2 py-1">
                            +{{ count($event->tags) - 3 }} lainnya
                        </span>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
async function toggleWishlist(eventId) {
    if (!@json(auth()->check())) {
        window.showNotification('Silakan login terlebih dahulu', 'warning');
        return;
    }

    const button = document.querySelector(`[data-event-id="${eventId}"]`);
    const icon = button.querySelector('svg');

    try {
        const response = await fetch(`/tickets${eventId}/wishlist`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (response.ok) {
            icon.classList.toggle('text-red-500');
            icon.classList.toggle('fill-current');
            window.showNotification(data.message, 'success');
        } else {
            window.showNotification(data.error || 'Terjadi kesalahan', 'error');
        }
    } catch (error) {
        window.showNotification('Terjadi kesalahan jaringan', 'error');
    }
}

function quickView(eventId) {
    // Open modal with event quick view
    // This would show a modal with basic event info and quick purchase option
    window.showNotification('Quick view akan segera tersedia!', 'info');
}
</script>
@endpush

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush
