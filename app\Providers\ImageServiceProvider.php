<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver as GdDriver;
use Intervention\Image\Drivers\Imagick\Driver as ImagickDriver;
use App\Services\ImageService;

class ImageServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('image', function () {
            $driver = config('image.driver', 'gd');

            return new ImageManager(
                $driver === 'imagick' ? new ImagickDriver() : new GdDriver()
            );
        });

        // Register alias for the Image facade
        $this->app->alias('image', ImageManager::class);

        // Register ImageService
        $this->app->singleton(ImageService::class, fn() => new ImageService());
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration file
        $this->publishes([
            __DIR__.'/../../config/image.php' => config_path('image.php'),
        ], 'image-config');

        // Create cache directory if it doesn't exist
        if (config('image.cache.enabled')) {
            $cachePath = config('image.cache.path');
            if (!file_exists($cachePath)) {
                mkdir($cachePath, 0755, true);
            }
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return ['image', ImageManager::class];
    }
}
