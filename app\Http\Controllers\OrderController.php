<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Services\PaymentGatewayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class OrderController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show payment page
     */
    public function payment(Order $order)
    {
        // Check if user owns this order
        if ($order->user_id !== auth()->id()) {
            abort(403, 'Anda tidak memiliki akses ke pesanan ini.');
        }

        // Check if order is still valid for payment
        if ($order->payment_status !== 'pending') {
            return redirect()->route('orders.show', $order)
                ->with('info', 'Pesanan ini sudah diproses.');
        }

        // Check if order has expired
        if ($order->expires_at && $order->expires_at < now()) {
            $this->expireOrder($order);
            return redirect()->route('tickets.my-tickets')
                ->with('error', 'Pesanan telah kedaluwarsa. Silakan buat pesanan baru.');
        }

        $order->load(['event', 'tickets']);

        // Get payment methods
        $paymentMethods = $this->getAvailablePaymentMethods();

        return view('orders.payment', compact('order', 'paymentMethods'));
    }

    /**
     * Process payment
     */
    public function processPayment(Request $request, Order $order)
    {
        // Check if user owns this order
        if ($order->user_id !== auth()->id()) {
            abort(403, 'Anda tidak memiliki akses ke pesanan ini.');
        }

        // Check if order is still valid for payment
        if ($order->payment_status !== 'pending') {
            return redirect()->route('orders.show', $order)
                ->with('error', 'Pesanan ini sudah diproses.');
        }

        // Check if order has expired
        if ($order->expires_at && $order->expires_at < now()) {
            $this->expireOrder($order);
            return redirect()->route('tickets.my-tickets')
                ->with('error', 'Pesanan telah kedaluwarsa.');
        }

        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:bank_transfer,credit_card,e_wallet,qris,virtual_account,cash',
            'payment_details' => 'required|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // Process payment using PaymentGatewayService
            $paymentGateway = new PaymentGatewayService();
            $paymentResult = $paymentGateway->processPayment($order, $request->payment_method, $request->payment_details ?? []);

            if ($paymentResult['success']) {
                // Update order status
                $order->update([
                    'payment_method' => $request->payment_method,
                    'payment_reference' => $paymentResult['reference'],
                    'payment_data' => $paymentResult['data'],
                ]);

                // For instant payment methods (cash), mark as paid immediately
                if ($request->payment_method === 'cash') {
                    $order->update([
                        'payment_status' => 'paid',
                        'paid_at' => now(),
                        'status' => 'confirmed',
                        'confirmed_at' => now(),
                    ]);

                    $this->sendPaymentConfirmation($order);
                }

                DB::commit();

                // Handle different payment methods
                if (isset($paymentResult['data']['payment_url'])) {
                    // Redirect to payment gateway
                    return redirect($paymentResult['data']['payment_url']);
                } elseif (isset($paymentResult['data']['qr_string']) || isset($paymentResult['data']['account_number'])) {
                    // Show payment details page
                    return redirect()->route('orders.payment-details', $order)
                        ->with('payment_data', $paymentResult['data'])
                        ->with('success', $paymentResult['message']);
                } else {
                    // Direct success (cash payment)
                    return redirect()->route('orders.success', $order)
                        ->with('success', $paymentResult['message']);
                }

            } else {
                DB::rollback();
                return back()->with('error', 'Pembayaran gagal: ' . $paymentResult['message'])
                    ->withInput();
            }

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Payment processing error', [
                'order_id' => $order->id,
                'payment_method' => $request->payment_method,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'Terjadi kesalahan saat memproses pembayaran. Silakan coba lagi.')
                ->withInput();
        }
    }

    /**
     * Show payment details page (for QR codes, virtual accounts, etc.)
     */
    public function paymentDetails(Order $order)
    {
        // Check if user owns this order
        if ($order->user_id !== auth()->id()) {
            abort(403, 'Anda tidak memiliki akses ke pesanan ini.');
        }

        // Check if order is still valid for payment
        if ($order->payment_status !== 'pending') {
            return redirect()->route('orders.show', $order)
                ->with('info', 'Pesanan ini sudah diproses.');
        }

        // Check if order has expired
        if ($order->expires_at && $order->expires_at < now()) {
            $this->expireOrder($order);
            return redirect()->route('tickets.my-tickets')
                ->with('error', 'Pesanan telah kedaluwarsa. Silakan buat pesanan baru.');
        }

        $order->load('event');
        $paymentData = session('payment_data', $order->payment_data);

        return view('orders.payment-details', compact('order', 'paymentData'));
    }

    /**
     * Check payment status via AJAX
     */
    public function checkPaymentStatus(Order $order)
    {
        // Check if user owns this order
        if ($order->user_id !== auth()->id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return response()->json([
            'status' => $order->payment_status,
            'paid_at' => $order->paid_at?->toISOString(),
            'reference' => $order->payment_reference,
        ]);
    }

    /**
     * Show payment success page
     */
    public function success(Order $order)
    {
        // Check if user owns this order
        if ($order->user_id !== auth()->id()) {
            abort(403, 'Anda tidak memiliki akses ke pesanan ini.');
        }

        // Check if order is paid
        if ($order->payment_status !== 'paid') {
            return redirect()->route('orders.payment', $order);
        }

        $order->load(['event', 'tickets']);

        return view('orders.success', compact('order'));
    }

    /**
     * Show order details
     */
    public function show(Order $order)
    {
        // Check if user owns this order
        if ($order->user_id !== auth()->id()) {
            abort(403, 'Anda tidak memiliki akses ke pesanan ini.');
        }

        $order->load(['event', 'tickets']);

        return view('orders.show', compact('order'));
    }

    /**
     * Show user's orders
     */
    public function index(Request $request)
    {
        $query = auth()->user()->orders()->with(['event']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment status
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // Search by order number or event
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('event', function ($eq) use ($search) {
                      $eq->where('title', 'like', "%{$search}%");
                  });
            });
        }

        $orders = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Get statistics
        $stats = [
            'total' => auth()->user()->orders()->count(),
            'pending' => auth()->user()->orders()->where('payment_status', 'pending')->count(),
            'paid' => auth()->user()->orders()->where('payment_status', 'paid')->count(),
            'total_spent' => auth()->user()->orders()->where('payment_status', 'paid')->sum('total_amount'),
        ];

        return view('orders.index', compact('orders', 'stats'));
    }

    /**
     * Cancel order
     */
    public function cancel(Order $order, Request $request)
    {
        // Check if user owns this order
        if ($order->user_id !== auth()->id()) {
            abort(403, 'Anda tidak memiliki akses ke pesanan ini.');
        }

        // Check if order can be cancelled
        if ($order->payment_status !== 'pending') {
            return back()->with('error', 'Pesanan yang sudah dibayar tidak dapat dibatalkan.');
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        try {
            DB::beginTransaction();

            // Update order status
            $order->update([
                'status' => 'cancelled',
                'payment_status' => 'cancelled',
                'cancelled_at' => now(),
                'cancellation_reason' => $request->reason,
            ]);

            // Cancel all tickets
            $order->tickets()->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'cancellation_reason' => $request->reason,
            ]);

            // Restore event capacity
            $order->event->increment('available_capacity', $order->quantity);

            DB::commit();

            return redirect()->route('orders.index')
                ->with('success', 'Pesanan berhasil dibatalkan.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Terjadi kesalahan saat membatalkan pesanan: ' . $e->getMessage());
        }
    }

    /**
     * Get available payment methods
     */
    private function getAvailablePaymentMethods(): array
    {
        return [
            'bank_transfer' => [
                'name' => 'Transfer Bank',
                'description' => 'Transfer ke rekening bank pilihan',
                'fee' => 0,
                'processing_time' => '1-3 jam',
                'icon' => 'bank',
                'popular' => true,
                'banks' => [
                    [
                        'name' => 'BCA',
                        'account' => '**********',
                        'holder' => 'PT TiXara Indonesia',
                        'code' => 'BCA',
                        'logo' => '/images/banks/bca.png'
                    ],
                    [
                        'name' => 'Mandiri',
                        'account' => '**********',
                        'holder' => 'PT TiXara Indonesia',
                        'code' => 'MANDIRI',
                        'logo' => '/images/banks/mandiri.png'
                    ],
                    [
                        'name' => 'BNI',
                        'account' => '**********',
                        'holder' => 'PT TiXara Indonesia',
                        'code' => 'BNI',
                        'logo' => '/images/banks/bni.png'
                    ],
                    [
                        'name' => 'BRI',
                        'account' => '**********',
                        'holder' => 'PT TiXara Indonesia',
                        'code' => 'BRI',
                        'logo' => '/images/banks/bri.png'
                    ],
                ]
            ],
            'credit_card' => [
                'name' => 'Kartu Kredit/Debit',
                'description' => 'Visa, Mastercard, JCB, American Express',
                'fee' => 2.9, // percentage
                'processing_time' => 'Instan',
                'icon' => 'credit-card',
                'popular' => false,
                'features' => [
                    'Pembayaran instan',
                    'Keamanan tinggi dengan 3D Secure',
                    'Cicilan 0% tersedia',
                    'Cashback untuk kartu tertentu'
                ]
            ],
            'e_wallet' => [
                'name' => 'E-Wallet',
                'description' => 'Dompet digital pilihan Indonesia',
                'fee' => 1.5, // percentage
                'processing_time' => 'Instan',
                'icon' => 'smartphone',
                'popular' => true,
                'providers' => [
                    [
                        'name' => 'GoPay',
                        'code' => 'GOPAY',
                        'logo' => '/images/ewallet/gopay.png',
                        'min_amount' => 10000,
                        'max_amount' => ********
                    ],
                    [
                        'name' => 'OVO',
                        'code' => 'OVO',
                        'logo' => '/images/ewallet/ovo.png',
                        'min_amount' => 10000,
                        'max_amount' => ********
                    ],
                    [
                        'name' => 'DANA',
                        'code' => 'DANA',
                        'logo' => '/images/ewallet/dana.png',
                        'min_amount' => 10000,
                        'max_amount' => ********
                    ],
                    [
                        'name' => 'LinkAja',
                        'code' => 'LINKAJA',
                        'logo' => '/images/ewallet/linkaja.png',
                        'min_amount' => 10000,
                        'max_amount' => ********
                    ],
                    [
                        'name' => 'ShopeePay',
                        'code' => 'SHOPEEPAY',
                        'logo' => '/images/ewallet/shopeepay.png',
                        'min_amount' => 10000,
                        'max_amount' => ********
                    ]
                ]
            ],
            'qris' => [
                'name' => 'QRIS',
                'description' => 'Scan QR Code dengan aplikasi bank/e-wallet',
                'fee' => 0.7, // percentage
                'processing_time' => 'Instan',
                'icon' => 'qr-code',
                'popular' => true,
                'features' => [
                    'Satu QR untuk semua aplikasi',
                    'Tidak perlu input nomor rekening',
                    'Biaya admin rendah',
                    'Konfirmasi otomatis'
                ]
            ],
            'virtual_account' => [
                'name' => 'Virtual Account',
                'description' => 'Bayar melalui ATM, mobile banking, atau internet banking',
                'fee' => 4000, // flat fee
                'processing_time' => '1-3 jam',
                'icon' => 'credit-card',
                'popular' => false,
                'banks' => ['BCA', 'Mandiri', 'BNI', 'BRI', 'Permata', 'CIMB']
            ],
            'cash' => [
                'name' => 'Bayar di Tempat',
                'description' => 'Bayar saat check-in event',
                'fee' => 0,
                'processing_time' => 'Saat event',
                'icon' => 'banknotes',
                'popular' => false,
                'note' => 'Hanya tersedia untuk event tertentu',
                'requirements' => [
                    'Datang 30 menit sebelum event',
                    'Bawa tiket digital dan identitas',
                    'Siapkan uang pas',
                    'Konfirmasi kehadiran H-1'
                ]
            ],
        ];
    }

    /**
     * Simulate payment processing
     */
    private function simulatePayment(Order $order, string $method, array $details): array
    {
        // Simulate different payment scenarios
        $scenarios = [
            'success' => 85, // 85% success rate
            'failed' => 10,  // 10% failure rate
            'pending' => 5,  // 5% pending rate
        ];

        $random = rand(1, 100);
        $cumulative = 0;

        foreach ($scenarios as $scenario => $percentage) {
            $cumulative += $percentage;
            if ($random <= $cumulative) {
                switch ($scenario) {
                    case 'success':
                        return [
                            'success' => true,
                            'reference' => 'PAY-' . strtoupper(uniqid()),
                            'message' => 'Pembayaran berhasil',
                            'data' => [
                                'method' => $method,
                                'details' => $details,
                                'processed_at' => now()->toISOString(),
                                'gateway_response' => 'SUCCESS',
                            ]
                        ];

                    case 'failed':
                        return [
                            'success' => false,
                            'message' => 'Pembayaran ditolak. Silakan coba lagi atau gunakan metode pembayaran lain.',
                            'data' => [
                                'method' => $method,
                                'error_code' => 'PAYMENT_DECLINED',
                                'processed_at' => now()->toISOString(),
                            ]
                        ];

                    case 'pending':
                        return [
                            'success' => false,
                            'message' => 'Pembayaran sedang diproses. Silakan tunggu beberapa saat.',
                            'data' => [
                                'method' => $method,
                                'status' => 'PENDING',
                                'processed_at' => now()->toISOString(),
                            ]
                        ];
                }
                break;
            }
        }

        // Default to success
        return [
            'success' => true,
            'reference' => 'PAY-' . strtoupper(uniqid()),
            'message' => 'Pembayaran berhasil',
            'data' => [
                'method' => $method,
                'details' => $details,
                'processed_at' => now()->toISOString(),
            ]
        ];
    }

    /**
     * Expire order
     */
    private function expireOrder(Order $order): void
    {
        DB::transaction(function () use ($order) {
            // Update order status
            $order->update([
                'status' => 'cancelled',
                'payment_status' => 'failed',
                'cancelled_at' => now(),
                'cancellation_reason' => 'Order expired',
            ]);

            // Cancel all tickets
            $order->tickets()->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'cancellation_reason' => 'Order expired',
            ]);

            // Restore event capacity
            $order->event->increment('available_capacity', $order->quantity);
        });
    }

    /**
     * Send payment confirmation
     */
    private function sendPaymentConfirmation(Order $order): void
    {
        // Implementation would send email/SMS/push notification
        \Log::info("Payment confirmation sent for order {$order->order_number}");
    }
}
