<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="theme-color" content="#A8D5BA">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="TiXara">

    <title><?php echo e($title ?? 'TiXara'); ?> - Platform Tiket Event Terpercaya</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=DM+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>


    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <style>
        :root {
            --primary: #A8D5BA;
            --secondary: #F4A261;
            --accent: #E76F51;
            --dark: #264653;
            --light: #F7F7F7;
            --gray-pastel: #D6D6D6;
            --green-light: #C7EACB;
            --success: #10B981;
            --warning: #F59E0B;
            --error: #EF4444;
            --info: #3B82F6;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light);
            padding-bottom: 80px; /* Space for floating footer */
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--accent);
        }

        /* Button styles */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border: none;
            color: white;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(168, 213, 186, 0.4);
        }

        /* Card hover effects */
        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        /* Gradient text */
        .gradient-text {
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            body {
                padding-bottom: 90px; /* More space for mobile footer */
            }
        }

        /* PWA specific styles */
        @media (display-mode: standalone) {
            body {
                padding-top: env(safe-area-inset-top);
                padding-bottom: calc(80px + env(safe-area-inset-bottom));
            }
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>

<body class="antialiased" x-data="{
    darkMode: false,
    showNotification: false,
    notificationMessage: '',
    notificationType: 'success',
    isOnline: navigator.onLine
}"
x-init="
    // Check online status
    window.addEventListener('online', () => isOnline = true);
    window.addEventListener('offline', () => isOnline = false);

    // Initialize AOS
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });
">

    <!-- Offline Indicator -->
    <div x-show="!isOnline"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         class="fixed top-0 left-0 right-0 bg-red-500 text-white text-center py-2 text-sm z-50">
        <div class="flex items-center justify-center space-x-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728"/>
            </svg>
            <span>Anda sedang offline. Beberapa fitur mungkin tidak tersedia.</span>
        </div>
    </div>

    <!-- Header -->
    <?php echo $__env->make('layouts.partials.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Main Content -->
    <main class="min-h-screen">
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Floating Footer Navigation -->
    <?php echo $__env->make('layouts.partials.floating-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Notification Toast -->
    <?php echo $__env->make('layouts.partials.notification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>


    <!-- Header Functionality -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/header-functionality.js']); ?>

    <!-- Alpine Store for Global State -->
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.store('loading', {
                show: false,
                toggle() {
                    this.show = !this.show;
                },
                start() {
                    this.show = true;
                },
                stop() {
                    this.show = false;
                }
            });

            Alpine.store('user', {
                <?php if(auth()->guard()->check()): ?>
                authenticated: true,
                data: <?php echo json_encode(auth()->user(), 15, 512) ?>,
                role: '<?php echo e(auth()->user()->role ?? ''); ?>',
                <?php else: ?>
                authenticated: false,
                data: null,
                role: null,
                <?php endif; ?>
            });
        });

        // PWA Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed');
                    });
            });
        }

        // Global notification function
        window.showNotification = function(message, type = 'success') {
            const event = new CustomEvent('show-notification', {
                detail: { message, type }
            });
            window.dispatchEvent(event);
        };
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/layouts/main.blade.php ENDPATH**/ ?>