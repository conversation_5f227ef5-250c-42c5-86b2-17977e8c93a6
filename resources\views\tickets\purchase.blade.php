@extends('layouts.main')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-light via-white to-green-light/30">
    
    <!-- Header -->
    <section class="pt-8 pb-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-6" aria-label="Breadcrumb" data-aos="fade-right">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="text-gray-500 hover:text-primary transition-colors duration-200">
                            Beranda
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <a href="{{ route('tickets.show', $event) }}" class="ml-1 text-gray-500 hover:text-primary transition-colors duration-200 md:ml-2">{{ $event->title }}</a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="ml-1 text-gray-700 md:ml-2">Beli Tiket</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <div class="text-center mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                    Beli Tiket Event
                </h1>
                <p class="text-lg text-gray-600" data-aos="fade-up" data-aos-delay="100">
                    Lengkapi informasi di bawah untuk membeli tiket
                </p>
            </div>
        </div>
    </section>

    <!-- Purchase Form -->
    <section class="pb-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <form method="POST" action="{{ route('tickets.store', $event) }}" 
                  x-data="ticketPurchase()" 
                  @submit="handleSubmit">
                @csrf
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    
                    <!-- Main Form -->
                    <div class="lg:col-span-2 space-y-6">
                        
                        <!-- Event Summary -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up">
                            <h2 class="text-xl font-bold text-gray-900 mb-4">Detail Event</h2>
                            <div class="flex items-start space-x-4">
                                <img src="{{ $event->poster_url }}" 
                                     alt="{{ $event->title }}" 
                                     class="w-20 h-20 object-cover rounded-lg">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 mb-2">{{ $event->title }}</h3>
                                    <div class="space-y-1 text-sm text-gray-600">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                            </svg>
                                            {{ $event->start_date->format('d M Y, H:i') }} WIB
                                        </div>
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            </svg>
                                            {{ $event->venue_name }}, {{ $event->city }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quantity Selection -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="100">
                            <h2 class="text-xl font-bold text-gray-900 mb-4">Jumlah Tiket</h2>
                            
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Pilih Jumlah Tiket
                                </label>
                                <div class="flex items-center space-x-4">
                                    <button type="button" 
                                            @click="decreaseQuantity()" 
                                            :disabled="quantity <= 1"
                                            class="w-12 h-12 rounded-full border-2 border-gray-300 flex items-center justify-center hover:border-primary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                                        </svg>
                                    </button>
                                    
                                    <div class="flex-1 text-center">
                                        <input type="number" 
                                               name="quantity" 
                                               x-model="quantity"
                                               min="1" 
                                               max="{{ $maxQuantity }}"
                                               class="w-20 text-center text-2xl font-bold border-0 focus:ring-0 focus:outline-none">
                                        <p class="text-sm text-gray-500 mt-1">
                                            Maksimal {{ $maxQuantity }} tiket
                                        </p>
                                    </div>
                                    
                                    <button type="button" 
                                            @click="increaseQuantity()" 
                                            :disabled="quantity >= {{ $maxQuantity }}"
                                            class="w-12 h-12 rounded-full border-2 border-gray-300 flex items-center justify-center hover:border-primary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            @if($existingTickets > 0)
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <p class="text-sm text-blue-700">
                                            Anda sudah memiliki {{ $existingTickets }} tiket untuk event ini.
                                        </p>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Attendee Information -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="200">
                            <h2 class="text-xl font-bold text-gray-900 mb-4">Informasi Peserta</h2>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="attendee_name" class="block text-sm font-medium text-gray-700 mb-2">
                                        Nama Lengkap <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" 
                                           id="attendee_name" 
                                           name="attendee_name" 
                                           value="{{ old('attendee_name', auth()->user()->name) }}"
                                           required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none transition-colors duration-200">
                                    @error('attendee_name')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="attendee_email" class="block text-sm font-medium text-gray-700 mb-2">
                                        Email <span class="text-red-500">*</span>
                                    </label>
                                    <input type="email" 
                                           id="attendee_email" 
                                           name="attendee_email" 
                                           value="{{ old('attendee_email', auth()->user()->email) }}"
                                           required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none transition-colors duration-200">
                                    @error('attendee_email')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="md:col-span-2">
                                    <label for="attendee_phone" class="block text-sm font-medium text-gray-700 mb-2">
                                        Nomor Telepon
                                    </label>
                                    <input type="tel" 
                                           id="attendee_phone" 
                                           name="attendee_phone" 
                                           value="{{ old('attendee_phone', auth()->user()->phone) }}"
                                           placeholder="08xxxxxxxxxx"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none transition-colors duration-200">
                                    @error('attendee_phone')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="300">
                            <h2 class="text-xl font-bold text-gray-900 mb-4">Metode Pembayaran</h2>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <label class="relative">
                                    <input type="radio" 
                                           name="payment_method" 
                                           value="bank_transfer" 
                                           class="sr-only peer"
                                           {{ old('payment_method') == 'bank_transfer' ? 'checked' : '' }}>
                                    <div class="p-4 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-gray-900">Transfer Bank</h3>
                                                <p class="text-sm text-gray-600">BCA, Mandiri, BNI</p>
                                            </div>
                                        </div>
                                    </div>
                                </label>

                                <label class="relative">
                                    <input type="radio" 
                                           name="payment_method" 
                                           value="e_wallet" 
                                           class="sr-only peer"
                                           {{ old('payment_method') == 'e_wallet' ? 'checked' : '' }}>
                                    <div class="p-4 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-gray-900">E-Wallet</h3>
                                                <p class="text-sm text-gray-600">GoPay, OVO, DANA</p>
                                            </div>
                                        </div>
                                    </div>
                                </label>

                                <label class="relative">
                                    <input type="radio" 
                                           name="payment_method" 
                                           value="credit_card" 
                                           class="sr-only peer"
                                           {{ old('payment_method') == 'credit_card' ? 'checked' : '' }}>
                                    <div class="p-4 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-gray-900">Kartu Kredit</h3>
                                                <p class="text-sm text-gray-600">Visa, Mastercard</p>
                                            </div>
                                        </div>
                                    </div>
                                </label>

                                <label class="relative">
                                    <input type="radio" 
                                           name="payment_method" 
                                           value="cash" 
                                           class="sr-only peer"
                                           {{ old('payment_method') == 'cash' ? 'checked' : '' }}>
                                    <div class="p-4 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-gray-900">Bayar di Tempat</h3>
                                                <p class="text-sm text-gray-600">Saat event berlangsung</p>
                                            </div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                            @error('payment_method')
                                <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Terms & Conditions -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="400">
                            <label class="flex items-start space-x-3">
                                <input type="checkbox" 
                                       name="terms_accepted" 
                                       value="1"
                                       required
                                       class="mt-1 rounded border-gray-300 text-primary focus:ring-primary">
                                <div class="text-sm text-gray-600">
                                    Saya menyetujui <a href="#" class="text-primary hover:text-accent">syarat dan ketentuan</a> 
                                    serta <a href="#" class="text-primary hover:text-accent">kebijakan privasi</a> TiXara.
                                </div>
                            </label>
                            @error('terms_accepted')
                                <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="lg:col-span-1">
                        <div class="sticky top-24">
                            <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-left">
                                <h2 class="text-xl font-bold text-gray-900 mb-6">Ringkasan Pesanan</h2>
                                
                                <!-- Price Breakdown -->
                                <div class="space-y-4 mb-6">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Harga tiket</span>
                                        <span x-text="formatPrice({{ $event->price }})" class="font-semibold"></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Jumlah</span>
                                        <span x-text="quantity + ' tiket'" class="font-semibold"></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Subtotal</span>
                                        <span x-text="formatPrice(subtotal)" class="font-semibold"></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Biaya admin</span>
                                        <span x-text="formatPrice(adminFee)" class="font-semibold"></span>
                                    </div>
                                    <div class="border-t border-gray-200 pt-4">
                                        <div class="flex justify-between items-center">
                                            <span class="text-lg font-bold text-gray-900">Total</span>
                                            <span x-text="formatPrice(total)" class="text-lg font-bold text-primary"></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Purchase Button -->
                                <button type="submit" 
                                        :disabled="loading"
                                        class="w-full bg-gradient-to-r from-primary to-secondary text-white py-4 rounded-xl font-bold text-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span x-show="!loading">Beli Tiket Sekarang</span>
                                    <span x-show="loading" class="flex items-center justify-center">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Memproses...
                                    </span>
                                </button>

                                <!-- Security Info -->
                                <div class="mt-6 pt-6 border-t border-gray-200">
                                    <div class="flex items-center text-sm text-gray-600">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                        </svg>
                                        Transaksi aman & terenkripsi
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
function ticketPurchase() {
    return {
        quantity: 1,
        loading: false,
        eventPrice: {{ $event->price }},
        maxQuantity: {{ $maxQuantity }},
        
        get subtotal() {
            return this.eventPrice * this.quantity;
        },
        
        get adminFee() {
            const fee = this.subtotal * 0.05;
            return Math.max(2500, Math.min(50000, fee));
        },
        
        get total() {
            return this.subtotal + this.adminFee;
        },
        
        increaseQuantity() {
            if (this.quantity < this.maxQuantity) {
                this.quantity++;
            }
        },
        
        decreaseQuantity() {
            if (this.quantity > 1) {
                this.quantity--;
            }
        },
        
        formatPrice(amount) {
            return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
        },
        
        handleSubmit(event) {
            this.loading = true;
            // Form will submit normally
        }
    }
}
</script>
@endpush
