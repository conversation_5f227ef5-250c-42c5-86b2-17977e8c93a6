<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;

class SettingsController extends Controller
{
    /**
     * Display admin settings
     */
    public function index()
    {
        // Get current settings
        $settings = [
            'app_name' => config('app.name'),
            'app_url' => config('app.url'),
            'app_timezone' => config('app.timezone'),
            'mail_driver' => config('mail.default'),
            'mail_from_address' => config('mail.from.address'),
            'mail_from_name' => config('mail.from.name'),
            'cache_driver' => config('cache.default'),
            'session_driver' => config('session.driver'),
            'queue_driver' => config('queue.default'),
            'filesystem_driver' => config('filesystems.default'),
        ];

        // System information
        $systemInfo = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_connection' => config('database.default'),
            'storage_disk' => config('filesystems.default'),
            'debug_mode' => config('app.debug'),
            'environment' => config('app.env'),
        ];

        // Cache information
        $cacheInfo = [
            'config_cached' => file_exists(base_path('bootstrap/cache/config.php')),
            'routes_cached' => file_exists(base_path('bootstrap/cache/routes-v7.php')),
            'views_cached' => count(glob(storage_path('framework/views/*.php'))) > 0,
            'events_cached' => file_exists(base_path('bootstrap/cache/events.php')),
        ];

        // Storage information
        $storageInfo = [
            'total_space' => $this->formatBytes(disk_total_space(storage_path())),
            'free_space' => $this->formatBytes(disk_free_space(storage_path())),
            'used_space' => $this->formatBytes(disk_total_space(storage_path()) - disk_free_space(storage_path())),
            'logs_size' => $this->getDirectorySize(storage_path('logs')),
            'cache_size' => $this->getDirectorySize(storage_path('framework/cache')),
            'sessions_size' => $this->getDirectorySize(storage_path('framework/sessions')),
        ];

        return view('pages.admin.settings.index', compact('settings', 'systemInfo', 'cacheInfo', 'storageInfo'));
    }

    /**
     * Update application settings
     */
    public function updateAppSettings(Request $request)
    {
        $request->validate([
            'app_name' => 'required|string|max:255',
            'app_url' => 'required|url',
            'app_timezone' => 'required|string',
        ]);

        // Update .env file
        $this->updateEnvFile([
            'APP_NAME' => '"' . $request->app_name . '"',
            'APP_URL' => $request->app_url,
            'APP_TIMEZONE' => $request->app_timezone,
        ]);

        // Clear config cache
        Artisan::call('config:clear');

        return redirect()->back()->with('success', 'Application settings updated successfully!');
    }

    /**
     * Update mail settings
     */
    public function updateMailSettings(Request $request)
    {
        $request->validate([
            'mail_driver' => 'required|string',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string|max:255',
            'mail_host' => 'nullable|string',
            'mail_port' => 'nullable|integer',
            'mail_username' => 'nullable|string',
            'mail_password' => 'nullable|string',
            'mail_encryption' => 'nullable|string',
        ]);

        $envUpdates = [
            'MAIL_MAILER' => $request->mail_driver,
            'MAIL_FROM_ADDRESS' => $request->mail_from_address,
            'MAIL_FROM_NAME' => '"' . $request->mail_from_name . '"',
        ];

        if ($request->filled('mail_host')) {
            $envUpdates['MAIL_HOST'] = $request->mail_host;
        }
        if ($request->filled('mail_port')) {
            $envUpdates['MAIL_PORT'] = $request->mail_port;
        }
        if ($request->filled('mail_username')) {
            $envUpdates['MAIL_USERNAME'] = $request->mail_username;
        }
        if ($request->filled('mail_password')) {
            $envUpdates['MAIL_PASSWORD'] = $request->mail_password;
        }
        if ($request->filled('mail_encryption')) {
            $envUpdates['MAIL_ENCRYPTION'] = $request->mail_encryption;
        }

        $this->updateEnvFile($envUpdates);

        // Clear config cache
        Artisan::call('config:clear');

        return redirect()->back()->with('success', 'Mail settings updated successfully!');
    }

    /**
     * Clear application cache
     */
    public function clearCache(Request $request)
    {
        $type = $request->get('type', 'all');

        try {
            switch ($type) {
                case 'config':
                    Artisan::call('config:clear');
                    $message = 'Configuration cache cleared successfully!';
                    break;
                case 'route':
                    Artisan::call('route:clear');
                    $message = 'Route cache cleared successfully!';
                    break;
                case 'view':
                    Artisan::call('view:clear');
                    $message = 'View cache cleared successfully!';
                    break;
                case 'cache':
                    Artisan::call('cache:clear');
                    $message = 'Application cache cleared successfully!';
                    break;
                case 'all':
                default:
                    Artisan::call('config:clear');
                    Artisan::call('route:clear');
                    Artisan::call('view:clear');
                    Artisan::call('cache:clear');
                    $message = 'All caches cleared successfully!';
                    break;
            }

            return redirect()->back()->with('success', $message);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to clear cache: ' . $e->getMessage());
        }
    }

    /**
     * Optimize application
     */
    public function optimize(Request $request)
    {
        try {
            // Clear all caches first
            Artisan::call('config:clear');
            Artisan::call('route:clear');
            Artisan::call('view:clear');
            Artisan::call('cache:clear');

            // Optimize for production
            if (config('app.env') === 'production') {
                Artisan::call('config:cache');
                Artisan::call('route:cache');
                Artisan::call('view:cache');
            }

            return redirect()->back()->with('success', 'Application optimized successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to optimize application: ' . $e->getMessage());
        }
    }

    /**
     * Clean storage
     */
    public function cleanStorage(Request $request)
    {
        $type = $request->get('type', 'logs');

        try {
            switch ($type) {
                case 'logs':
                    $this->cleanDirectory(storage_path('logs'));
                    $message = 'Log files cleaned successfully!';
                    break;
                case 'cache':
                    $this->cleanDirectory(storage_path('framework/cache'));
                    $message = 'Cache files cleaned successfully!';
                    break;
                case 'sessions':
                    $this->cleanDirectory(storage_path('framework/sessions'));
                    $message = 'Session files cleaned successfully!';
                    break;
                case 'temp':
                    $this->cleanDirectory(storage_path('app/temp'));
                    $message = 'Temporary files cleaned successfully!';
                    break;
                default:
                    return redirect()->back()->with('error', 'Invalid storage type specified.');
            }

            return redirect()->back()->with('success', $message);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to clean storage: ' . $e->getMessage());
        }
    }

    /**
     * Update .env file
     */
    private function updateEnvFile(array $data)
    {
        $envFile = base_path('.env');
        $envContent = file_get_contents($envFile);

        foreach ($data as $key => $value) {
            $pattern = "/^{$key}=.*/m";
            $replacement = "{$key}={$value}";

            if (preg_match($pattern, $envContent)) {
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                $envContent .= "\n{$replacement}";
            }
        }

        file_put_contents($envFile, $envContent);
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Get directory size
     */
    private function getDirectorySize($directory)
    {
        if (!is_dir($directory)) {
            return '0 B';
        }

        $size = 0;
        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($files as $file) {
            $size += $file->getSize();
        }

        return $this->formatBytes($size);
    }

    /**
     * Clean directory
     */
    private function cleanDirectory($directory)
    {
        if (!is_dir($directory)) {
            return;
        }

        $files = glob($directory . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
    }
}
