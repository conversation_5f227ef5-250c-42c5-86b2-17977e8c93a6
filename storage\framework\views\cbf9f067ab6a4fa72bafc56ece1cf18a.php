<!-- Header Component with Alpine.js Data -->
<header class="fixed top-0 left-0 right-0 z-40 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-pasta-cream/50 dark:border-gray-700 shadow-sm"
        x-data="{
            // Mobile menu state
            mobileMenuOpen: false,
            searchOpen: false,

            // Search functionality
            searchQuery: '',
            searchResults: [],
            searchLoading: false,

            // Theme management
            darkMode: localStorage.getItem('darkMode') === 'true' || false,
            themeColor: localStorage.getItem('themeColor') || 'green',
            availableThemes: {
                green: { name: 'Hijau Pasta', primary: '#A8D5BA' },
                blue: { name: '<PERSON><PERSON><PERSON>', primary: '#6366F1' },
                purple: { name: '<PERSON><PERSON><PERSON>', primary: '#8B5CF6' },
                pink: { name: 'Pink Soft', primary: '#EC4899' },
                orange: { name: 'Orange Hangat', primary: '#F97316' },
                red: { name: '<PERSON><PERSON>', primary: '#EF4444' },
                monochrome: { name: '<PERSON><PERSON><PERSON>', primary: '#6B7280' },
                dark: { name: '<PERSON><PERSON><PERSON>', primary: '#1F2937' }
            },

            // Time display
            currentTime: '',

            // Notification count
            notificationCount: 0,

            // Initialize component
            init() {
                this.updateTime();
                this.applyTheme();
                this.initializeNotifications();

                // Update time every second
                setInterval(() => {
                    this.updateTime();
                }, 1000);

                // Initialize theme manager if available
                if (window.themeManager) {
                    window.themeManager.init();
                }

                // Initialize live notifications if available
                if (window.liveNotifications) {
                    window.liveNotifications.init();
                }
            },

            // Update current time
            updateTime() {
                const now = new Date();
                this.currentTime = now.toLocaleTimeString('id-ID', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            },

            // Theme management functions
            toggleDarkMode() {
                this.darkMode = !this.darkMode;
                this.applyTheme();
                this.saveThemeSettings();

                // Dispatch event for theme manager
                window.dispatchEvent(new CustomEvent('themeChanged', {
                    detail: { darkMode: this.darkMode, themeColor: this.themeColor }
                }));

                // Show notification
                window.showNotification(
                    this.darkMode ? 'Mode gelap diaktifkan' : 'Mode terang diaktifkan',
                    'success',
                    3000
                );
            },

            changeTheme(color) {
                this.themeColor = color;
                this.applyTheme();
                this.saveThemeSettings();

                // Dispatch event for theme manager
                window.dispatchEvent(new CustomEvent('themeChanged', {
                    detail: { darkMode: this.darkMode, themeColor: this.themeColor }
                }));

                // Show notification
                const themeName = this.availableThemes[color].name;
                window.showNotification(`Tema ${themeName} diterapkan`, 'success', 3000);
            },

            applyTheme() {
                const root = document.documentElement;
                const theme = this.availableThemes[this.themeColor];

                // Apply dark mode class
                if (this.darkMode || this.themeColor === 'dark') {
                    root.classList.add('dark');
                } else {
                    root.classList.remove('dark');
                }

                // Apply theme colors
                if (theme) {
                    root.style.setProperty('--color-primary', theme.primary);
                }

                // Update meta theme color for mobile browsers
                const metaThemeColor = document.querySelector('meta[name=theme-color]');
                if (metaThemeColor) {
                    metaThemeColor.setAttribute('content', theme.primary);
                }
            },

            saveThemeSettings() {
                localStorage.setItem('darkMode', this.darkMode);
                localStorage.setItem('themeColor', this.themeColor);
            },

            // Search functionality
            async searchEvents() {
                if (this.searchQuery.length < 2) {
                    this.searchResults = [];
                    return;
                }

                this.searchLoading = true;

                try {
                    const response = await fetch(`/api/search?q=${encodeURIComponent(this.searchQuery)}`, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        this.searchResults = data.results || [];
                    } else {
                        this.searchResults = [];
                    }
                } catch (error) {
                    console.error('Search error:', error);
                    this.searchResults = [];
                } finally {
                    this.searchLoading = false;
                }
            },

            // Notification management
            initializeNotifications() {
                // Initialize notification count from server or localStorage
                this.updateNotificationCount();

                // Listen for new notifications
                window.addEventListener('newNotification', (event) => {
                    this.notificationCount = event.detail.count || 0;
                    this.updateNotificationBadge();
                });
            },

            updateNotificationCount() {
                // This would typically fetch from server
                const badge = document.querySelector('.notification-badge');
                if (badge) {
                    const count = parseInt(badge.textContent) || 0;
                    this.notificationCount = count;
                    this.updateNotificationBadge();
                }
            },

            updateNotificationBadge() {
                const badge = document.querySelector('.notification-badge');
                if (badge) {
                    if (this.notificationCount > 0) {
                        badge.style.display = 'flex';
                        badge.textContent = this.notificationCount > 99 ? '99+' : this.notificationCount;
                    } else {
                        badge.style.display = 'none';
                    }
                }
            }
        }"
        x-init="init()">

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex items-center space-x-3">
                <a href="<?php echo e(route('home')); ?>" class="flex items-center space-x-3 group">
                    <div class="relative w-12 h-12 bg-gradient-to-br from-primary via-pasta-sage to-accent rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg group-hover:shadow-xl">
                        <div class="absolute inset-0 bg-gradient-to-br from-pasta-cream/20 to-pasta-mint/20 rounded-2xl"></div>
                        <svg class="w-7 h-7 text-white relative z-10 drop-shadow-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                        </svg>
                        <div class="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-br from-pasta-peach to-pasta-salmon rounded-full animate-pulse"></div>
                    </div>
                    <div class="flex flex-col">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-primary via-pasta-sage to-accent bg-clip-text text-transparent group-hover:from-accent group-hover:to-primary transition-all duration-300">
                            TiXara
                        </h1>
                        <span class="text-xs text-gray-500 dark:text-gray-400 font-medium tracking-wide">Event Platform</span>
                    </div>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="<?php echo e(route('home')); ?>"
                   class="relative text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-all duration-300 font-medium group <?php echo e(request()->routeIs('home') ? 'text-primary dark:text-primary' : ''); ?>">
                    <span class="relative z-10">Beranda</span>
                    <div class="absolute inset-0 bg-gradient-to-r from-pasta-cream to-pasta-mint opacity-0 group-hover:opacity-20 rounded-lg transition-opacity duration-300"></div>
                    <?php if(request()->routeIs('home')): ?>
                        <div class="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-accent rounded-full"></div>
                    <?php endif; ?>
                </a>
                <a href="<?php echo e(route('tickets.index')); ?>"
                   class="relative text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-all duration-300 font-medium group <?php echo e(request()->routeIs('tickets.*') ? 'text-primary dark:text-primary' : ''); ?>">
                    <span class="relative z-10">Tiket</span>
                    <div class="absolute inset-0 bg-gradient-to-r from-pasta-cream to-pasta-mint opacity-0 group-hover:opacity-20 rounded-lg transition-opacity duration-300"></div>
                    <?php if(request()->routeIs('tickets.*')): ?>
                        <div class="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-accent rounded-full"></div>
                    <?php endif; ?>
                </a>

                <!-- Search Bar -->
                <div class="relative" x-data="{ showSearchResults: false }">
                    <div class="relative">
                        <input type="text"
                               x-model="searchQuery"
                               @input.debounce.300ms="searchEvents()"
                               @focus="showSearchResults = true"
                               @click.away="showSearchResults = false"
                               placeholder="Cari event..."
                               class="w-64 px-4 py-2 pl-10 pr-4 rounded-xl border-2 border-pasta-cream/50 dark:border-gray-600 bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-300 backdrop-blur-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="w-4 h-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        <div x-show="searchLoading" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                        </div>
                    </div>

                    <!-- Search Results Dropdown -->
                    <div x-show="showSearchResults && (searchResults.length > 0 || searchQuery.length > 0)"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform scale-95"
                         x-transition:enter-end="opacity-100 transform scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform scale-100"
                         x-transition:leave-end="opacity-0 transform scale-95"
                         class="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-pasta-cream/50 dark:border-gray-600 py-2 z-50 backdrop-blur-sm max-h-64 overflow-y-auto">

                        <template x-if="searchResults.length === 0 && searchQuery.length > 0 && !searchLoading">
                            <div class="px-4 py-3 text-center text-gray-500 dark:text-gray-400">
                                <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                                <p class="text-sm">Tidak ada hasil ditemukan</p>
                            </div>
                        </template>

                        <template x-for="result in searchResults" :key="result.id">
                            <a :href="result.url" class="block px-4 py-3 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 transition-colors duration-200">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="result.title"></p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400" x-text="result.description"></p>
                                    </div>
                                </div>
                            </a>
                        </template>
                    </div>
                </div>

                <?php if(auth()->guard()->check()): ?>
                    <?php if(auth()->user()->isPenjual() || auth()->user()->isAdmin()): ?>
                        <a href="<?php echo e(route('organizer.dashboard')); ?>"
                           class="relative text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-all duration-300 font-medium group <?php echo e(request()->routeIs('organizer.*') ? 'text-primary dark:text-primary' : ''); ?>">
                            <span class="relative z-10">Dashboard</span>
                            <div class="absolute inset-0 bg-gradient-to-r from-pasta-cream to-pasta-mint opacity-0 group-hover:opacity-20 rounded-lg transition-opacity duration-300"></div>
                            <?php if(request()->routeIs('organizer.*')): ?>
                                <div class="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-accent rounded-full"></div>
                            <?php endif; ?>
                        </a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>

            <!-- Right Side Actions -->
            <div class="flex items-center space-x-3">
                <!-- Clock -->
                <div class="hidden lg:flex items-center space-x-2 px-3 py-2 rounded-xl bg-pasta-cream/50 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span class="text-sm font-medium" x-text="currentTime"></span>
                </div>

                <!-- Mobile Search Button -->
                <button @click="searchOpen = !searchOpen"
                        class="md:hidden relative p-2.5 rounded-xl bg-pasta-cream/50 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-mint/50 dark:hover:bg-gray-600 transition-all duration-300 group shadow-sm hover:shadow-md">
                    <svg class="w-5 h-5 transition-transform duration-300" :class="{ 'rotate-90': searchOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                    <div class="absolute inset-0 bg-gradient-to-r from-pasta-peach/20 to-pasta-salmon/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                <?php if(auth()->guard()->check()): ?>
                    <!-- Notifications -->
                    <div class="relative" x-data="{ showNotifications: false }">
                        <button @click="showNotifications = !showNotifications"
                                class="relative p-2.5 rounded-xl bg-pasta-cream/50 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-mint/50 dark:hover:bg-gray-600 transition-all duration-300 group shadow-sm hover:shadow-md">
                            <svg class="w-5 h-5 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                            </svg>
                            <!-- Notification Badge -->
                            <span class="notification-badge absolute -top-1 -right-1 bg-gradient-to-r from-pasta-salmon to-pasta-peach text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse shadow-lg"
                                  style="display: none;">0</span>
                            <div class="absolute inset-0 bg-gradient-to-r from-pasta-peach/20 to-pasta-salmon/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </button>

                        <!-- Notifications Dropdown -->
                        <div x-show="showNotifications"
                             @click.away="showNotifications = false"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-95"
                             class="absolute right-0 mt-3 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-pasta-cream/50 dark:border-gray-600 py-2 z-50 backdrop-blur-sm"
                             :class="{ 'w-80': window.innerWidth >= 768, 'w-screen max-w-sm -right-4': window.innerWidth < 768 }">
                            <div class="px-4 py-3 border-b border-pasta-cream/50 dark:border-gray-600 flex items-center justify-between bg-gradient-to-r from-pasta-cream/20 to-pasta-mint/20 dark:from-gray-700/50 dark:to-gray-600/50">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                                    </svg>
                                    <span>Notifikasi</span>
                                </h3>
                                <button onclick="markAllNotificationsAsRead()"
                                        class="text-xs text-primary hover:text-accent font-medium px-3 py-1 rounded-lg bg-primary/10 hover:bg-primary/20 transition-all duration-300">
                                    Tandai Semua Dibaca
                                </button>
                            </div>
                            <div class="notifications-dropdown-content max-h-64 overflow-y-auto">
                                <!-- Dynamic Notifications will be loaded here -->
                                <div id="notifications-list">
                                    <!-- Sample Notification -->
                                    <div class="px-4 py-3 hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 cursor-pointer border-b border-pasta-cream/30 dark:border-dark-600 transition-all duration-300 group">
                                        <div class="flex items-start space-x-3">
                                            <div class="w-3 h-3 bg-gradient-to-r from-pasta-salmon to-pasta-peach rounded-full mt-2 shadow-sm group-hover:scale-110 transition-transform duration-300"></div>
                                            <div class="flex-1">
                                                <div class="flex items-center justify-between">
                                                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Selamat datang!</h4>
                                                    <span class="text-xs text-gray-500 dark:text-gray-400">Baru saja</span>
                                                </div>
                                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Terima kasih telah bergabung dengan TiXara. Jelajahi event menarik di sekitar Anda!</p>
                                                <div class="flex items-center space-x-2 mt-2">
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                        </svg>
                                                        Info
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Empty State -->
                                    <div id="notifications-empty" class="px-4 py-8 text-center text-gray-500 dark:text-gray-400" style="display: none;">
                                        <svg class="w-12 h-12 mx-auto mb-3 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                                        </svg>
                                        <p class="text-sm font-medium">Tidak ada notifikasi</p>
                                        <p class="text-xs mt-1">Notifikasi baru akan muncul di sini</p>
                                    </div>
                                </div>
                            </div>
                            <div class="px-4 py-3 border-t border-pasta-cream/50 dark:border-dark-600 bg-gradient-to-r from-pasta-cream/10 to-pasta-mint/10 dark:from-dark-700/30 dark:to-dark-600/30">
                                <a href="<?php echo e(route('notifications.index')); ?>" class="text-sm text-primary hover:text-accent font-medium flex items-center space-x-2 group">
                                    <span>Lihat semua notifikasi</span>
                                    <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="relative" x-data="{ showUserMenu: false }">
                        <button @click="showUserMenu = !showUserMenu"
                                class="flex items-center space-x-3 p-2 rounded-xl bg-pasta-cream/50 dark:bg-dark-700 hover:bg-pasta-mint/50 dark:hover:bg-dark-600 transition-all duration-300 group shadow-sm hover:shadow-md">
                            <div class="relative">
                                <img src="<?php echo e(auth()->user()->avatar_url); ?>"
                                     alt="<?php echo e(auth()->user()->name); ?>"
                                     class="w-9 h-9 rounded-xl object-cover ring-2 ring-primary/20 group-hover:ring-primary/40 transition-all duration-300">
                                <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-gradient-to-r from-primary to-accent rounded-full border-2 border-white dark:border-dark-700"></div>
                            </div>
                            <div class="hidden md:block">
                                <span class="text-sm font-semibold text-gray-700 dark:text-gray-200 block"><?php echo e(auth()->user()->name); ?></span>
                                <span class="text-xs text-gray-500 dark:text-gray-400"><?php echo e(ucfirst(auth()->user()->role)); ?></span>
                            </div>
                            <svg class="w-4 h-4 text-gray-400 dark:text-gray-500 group-hover:text-primary transition-all duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>

                        <!-- User Dropdown -->
                        <div x-show="showUserMenu"
                             @click.away="showUserMenu = false"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-95"
                             class="absolute right-0 mt-3 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-pasta-cream/50 dark:border-gray-600 py-2 z-50 backdrop-blur-sm"
                             :class="{ 'w-56': window.innerWidth >= 768, 'w-screen max-w-sm -right-4': window.innerWidth < 768 }">
                            <div class="px-4 py-3 border-b border-pasta-cream/50 dark:border-dark-600 bg-gradient-to-r from-pasta-cream/20 to-pasta-mint/20 dark:from-dark-700/50 dark:to-dark-600/50">
                                <div class="flex items-center space-x-3">
                                    <img src="<?php echo e(auth()->user()->avatar_url); ?>"
                                         alt="<?php echo e(auth()->user()->name); ?>"
                                         class="w-10 h-10 rounded-xl object-cover ring-2 ring-primary/30">
                                    <div>
                                        <p class="text-sm font-semibold text-gray-900 dark:text-gray-100"><?php echo e(auth()->user()->name); ?></p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400"><?php echo e(auth()->user()->email); ?></p>
                                    </div>
                                </div>
                                <span class="inline-block px-3 py-1 text-xs bg-gradient-to-r from-primary/20 to-accent/20 text-primary dark:text-primary-300 rounded-full mt-2 font-medium">
                                    <?php echo e(ucfirst(auth()->user()->role)); ?>

                                </span>
                            </div>
                            <div class="py-1">
                                <a href="<?php echo e(route('profile')); ?>" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                    <span>Profil Saya</span>
                                </a>
                                <a href="<?php echo e(route('tickets.my-tickets')); ?>" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                                    </svg>
                                    <span>Tiket Saya</span>
                                </a>
                                <a href="<?php echo e(route('history-pembelian')); ?>" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                    </svg>
                                    <span>Riwayat Pembelian</span>
                                </a>
                            </div>
                            <div class="border-t border-pasta-cream/50 dark:border-dark-600 mt-1 pt-1">
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="flex items-center space-x-3 w-full px-4 py-2.5 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-300 group">
                                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                                        </svg>
                                        <span>Logout</span>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Guest Actions -->
                    <div class="hidden md:flex items-center space-x-3">
                        <a href="<?php echo e(route('login')); ?>"
                           class="relative px-5 py-2.5 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary rounded-xl bg-pasta-cream/50 dark:bg-dark-700 hover:bg-pasta-mint/50 dark:hover:bg-dark-600 transition-all duration-300 font-medium group shadow-sm hover:shadow-md">
                            <span class="relative z-10">Masuk</span>
                            <div class="absolute inset-0 bg-gradient-to-r from-pasta-cream/20 to-pasta-mint/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </a>
                        <a href="<?php echo e(route('register')); ?>"
                           class="relative bg-gradient-to-r from-primary via-pasta-sage to-accent text-white hover:shadow-lg px-6 py-2.5 rounded-xl transition-all duration-300 font-medium transform hover:scale-105 hover:-translate-y-0.5 shadow-md group overflow-hidden">
                            <span class="relative z-10 flex items-center space-x-2">
                                <span>Daftar</span>
                                <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                                </svg>
                            </span>
                            <div class="absolute inset-0 bg-gradient-to-r from-accent to-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </a>
                    </div>
                <?php endif; ?>

                <!-- Mobile menu button -->
                <button @click="mobileMenuOpen = !mobileMenuOpen"
                        class="md:hidden relative p-2.5 rounded-xl bg-pasta-cream/50 dark:bg-dark-700 text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-mint/50 dark:hover:bg-dark-600 transition-all duration-300 group shadow-sm hover:shadow-md">
                    <svg class="w-5 h-5 transition-transform duration-300" :class="{ 'rotate-90': mobileMenuOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                    <div class="absolute inset-0 bg-gradient-to-r from-pasta-peach/20 to-pasta-salmon/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Search Bar -->
    <div x-show="searchOpen"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2"
         class="lg:hidden border-t border-pasta-cream/50 dark:border-gray-600 bg-gradient-to-r from-pasta-cream/10 to-pasta-mint/10 dark:from-gray-800/50 dark:to-gray-700/50 px-4 py-4">
        <div class="relative group" x-data="{ showMobileSearchResults: false }">
            <input type="text"
                   x-model="searchQuery"
                   @input.debounce.300ms="searchEvents()"
                   @focus="showMobileSearchResults = true"
                   @click.away="showMobileSearchResults = false"
                   placeholder="Cari Tiket Event Favorit Anda..."
                   class="w-full px-4 py-3 pl-12 pr-10 rounded-2xl border-2 border-pasta-cream dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary focus:outline-none focus:ring-4 focus:ring-primary/20 transition-all duration-300 shadow-sm">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-gray-400 dark:text-gray-500 group-focus-within:text-primary transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
            </div>
            <div x-show="searchLoading" class="absolute inset-y-0 right-0 pr-4 flex items-center">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
            </div>

            <!-- Mobile Search Results -->
            <div x-show="showMobileSearchResults && (searchResults.length > 0 || searchQuery.length > 0)"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95"
                 class="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-pasta-cream/50 dark:border-gray-600 py-2 z-50 backdrop-blur-sm max-h-64 overflow-y-auto">

                <template x-if="searchResults.length === 0 && searchQuery.length > 0 && !searchLoading">
                    <div class="px-4 py-3 text-center text-gray-500 dark:text-gray-400">
                        <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <p class="text-sm">Tidak ada hasil ditemukan</p>
                    </div>
                </template>

                <template x-for="result in searchResults" :key="result.id">
                    <a :href="result.url" class="block px-4 py-3 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 transition-colors duration-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="result.title"></p>
                                <p class="text-xs text-gray-500 dark:text-gray-400" x-text="result.description"></p>
                            </div>
                        </div>
                    </a>
                </template>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div x-show="mobileMenuOpen"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2"
         class="md:hidden bg-gradient-to-b from-white to-pasta-cream/30 dark:from-gray-800 dark:to-gray-900 border-t border-pasta-cream/50 dark:border-gray-600 backdrop-blur-sm">
        <div class="px-4 py-6 space-y-3">
            <!-- Mobile Theme Selector -->
            <div class="mb-4 p-3 bg-pasta-cream/30 dark:bg-gray-700/30 rounded-xl">
                <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"/>
                    </svg>
                    <span>Tema & Mode</span>
                </h4>
                <div class="grid grid-cols-2 gap-2 mb-3">
                    <template x-for="(theme, color) in availableThemes" :key="color">
                        <button @click="changeTheme(color)"
                                class="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm transition-colors duration-200"
                                :class="{ 'bg-primary/20 text-primary': themeColor === color, 'hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300': themeColor !== color }">
                            <div class="w-3 h-3 rounded-full border border-gray-300 dark:border-gray-600"
                                 :style="`background-color: ${theme.primary}`"></div>
                            <span x-text="theme.name"></span>
                        </button>
                    </template>
                </div>
                <button @click="toggleDarkMode()"
                        class="w-full flex items-center justify-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors duration-200">
                    <svg x-show="!darkMode" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
                    </svg>
                    <svg x-show="darkMode" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                    </svg>
                    <span x-text="darkMode ? 'Mode Terang' : 'Mode Gelap'"></span>
                </button>
            </div>

            <!-- Mobile Clock -->
            <div class="mb-4 p-3 bg-pasta-cream/30 dark:bg-gray-700/30 rounded-xl flex items-center justify-center space-x-2">
                <svg class="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span class="text-sm font-medium text-gray-600 dark:text-gray-300" x-text="currentTime"></span>
            </div>
            <a href="<?php echo e(route('home')); ?>" class="flex items-center space-x-3 p-3 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 rounded-xl transition-all duration-300 font-medium group">
                <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                </svg>
                <span>Beranda</span>
            </a>
            <a href="<?php echo e(route('tickets.index')); ?>" class="flex items-center space-x-3 p-3 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 rounded-xl transition-all duration-300 font-medium group">
                <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                </svg>
                <span>Tickets</span>
            </a>
            <a href="#categories" class="flex items-center space-x-3 p-3 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 rounded-xl transition-all duration-300 font-medium group">
                <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
                <span>Kategori</span>
            </a>

            <?php if(auth()->guard()->check()): ?>
                <?php if(auth()->user()->isPenjual() || auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(route('organizer.dashboard')); ?>" class="flex items-center space-x-3 p-3 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 rounded-xl transition-all duration-300 font-medium group">
                        <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        <span>Dashboard</span>
                    </a>
                <?php endif; ?>
            <?php else: ?>
                <div class="pt-4 border-t border-pasta-cream/50 dark:border-dark-600 space-y-3">
                    <a href="<?php echo e(route('login')); ?>"
                       class="block w-full text-center bg-pasta-cream/50 dark:bg-dark-700 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary px-4 py-3 rounded-xl transition-all duration-300 font-medium hover:bg-pasta-mint/50 dark:hover:bg-dark-600">
                        Masuk
                    </a>
                    <a href="<?php echo e(route('register')); ?>"
                       class="block w-full text-center bg-gradient-to-r from-primary via-pasta-sage to-accent text-white px-4 py-3 rounded-xl transition-all duration-300 font-medium hover:shadow-lg transform hover:scale-105">
                        Daftar
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</header>

<!-- Header Spacer -->
<div class="h-16"></div>
<?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/layouts/partials/header.blade.php ENDPATH**/ ?>